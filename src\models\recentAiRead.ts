import { Reducer } from 'umi';


export interface recentAiReadState {
  list: [];
}

export interface recentAiReadType {
  namespace: 'recentAiRead';
  state: recentAiReadState;
  reducers: {
    updateList: Reducer<recentAiReadState>;
    fetchList: Reducer<recentAiReadState>;
  };
}

let isfetched = false ;
const recentAiRead: recentAiReadType = {
  namespace: 'recentAiRead',

  state: {
    list: [],
  },
  reducers: {
    fetchList(state, { payload }) {
      if(isfetched) return state;
      isfetched = true ;

      let ulist = [ ];
      var s = localStorage.getItem('_recentAiRead_') ;
      if(s){
        try{
          ulist = JSON.parse(s);
        }catch(e){
          ulist = [];
        }
      }

      return {
        ...state,
        list: ulist,
      };
    },

    updateList(state, { payload }) {
      var ulist = state.list.map(it=>it) ;
      var areadInd = ulist.findIndex(it=>it.docurl == payload.docurl) ;
      var aread = {};

      if(areadInd > -1){
        aread = ulist[areadInd];
        ulist.splice(areadInd,1) ;
      }

      for(var k in payload) aread[k] = payload[k] ;
      aread.time = new Date().getTime();
      aread.type = '' ;

      if(payload.docurl.endsWith('.pdf')) aread.type = 'pdf' ;
      else if(payload.docurl.endsWith('.doc') || payload.docurl.endsWith('.docx')) aread.type = 'doc' ;
      else if(payload.docurl.endsWith('.ppt') || payload.docurl.endsWith('.pptx')) aread.type = 'ppt' ;

      ulist.unshift(aread);

      while(ulist.length > 6){
        ulist.pop();
      }

      localStorage.setItem('_recentAiRead_',JSON.stringify(ulist)) ;

      return {
        ...state,
        list: ulist,
      };
    },


  },
};

export default recentAiRead;
