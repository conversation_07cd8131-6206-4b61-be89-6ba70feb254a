.wrap {
	display: flex;
	height: 100%;
	width: 100%;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	transform-origin: center center;
	opacity: 1;
	transform: scale(1);
	
	&.closing {
		opacity: 0;
		transform: scale(0.8);
		pointer-events: none;
	}
	.chatContainer {
		padding: 0 15px;
		width: 40%;
		height: 100%;
		overflow: auto;
		border-right: 1px solid #eaecef;
	}
	.ptop {
		padding-top: 50px;
	}
	
	.pdfContainer {
		flex: 1;
		height: 100%;
		position: relative;
		width: 70%;
	}
}

.hidePage {
	.chatContainer {
		width: 100%;
	}
	.pdfContainer {
		display: none;
	}
}

.sidebarControl {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 1000;
	
	button {
		background-color: rgba(0, 0, 0, 0.7);
		color: white;
		border: none;
		border-radius: 4px;
		padding: 8px 16px;
		cursor: pointer;
		transition: all 0.3s;
		
		&:hover {
			background-color: rgba(0, 0, 0, 0.9);
		}
	}
}
.loadingContainer {
	position: absolute;
    bottom: 7%;
    right: 50px;
	display: inline-flex;
	align-items: center;
	padding: 8px 16px;
	background-color: #f5f7fa;
	border-radius: 20px;
	gap: 8px;
	border: 1px solid #1677ff;
  }
  
  .icon {
	width: 16px;
	height: 16px;
	fill: #1677ff;
	// animation: rotate 1s linear infinite;
  }
  
  .text {
	color: #1677ff;
	font-size: 14px;
  }
  
  @keyframes rotate {
	from {
	  transform: rotate(0deg);
	}
	to {
	  transform: rotate(360deg);
	}
  }
