.aiChat {
  margin: 0 auto;
  width: 100%;
  height: 100%;
  max-width: 100%;
  padding: 0;

  :global(.semi-markdownRender) {
    // width: 760px;
    padding: 10px 16px;

    &:empty {
      padding: 0;
    }

    ol {
      margin-left: 12px;
    }
  }

  :global(.semi-chat-hints) {
    margin-top: 16px;
  }

  :global(.semi-chat-chatBox-content-loading) {
    padding: 10px;
  }

  :global(.semi-chat-container) {
    overflow-x: hidden;
    padding: 0 calc((100% - 800px) / 2);
  }

  :global(.semi-chat-action-content.semi-button) {
    bottom: 24px;
  }

  :global(.semi-input-textarea) {
    padding: 0;

    &::placeholder {
      font-size: 14px;
      color: #ccc;
    }
  }

  :global(.semi-chat-content) {
    padding-bottom: 12px;
  }

  :global(.semi-chat-chatBox-right .semi-chat-chatBox-wrap) {
    width: 100%;
  }

}

.homeChat {
  height: 100%;

  :global(.semi-chat-inner) {
    justify-content: center;
    transform: translateY(-56px);
  }


  :global(.semi-chat-content) {
    display: none;
  }
}

.templateHeaderWrapper {
  max-width: 799px;
  position: absolute;
  border-radius: 16px;
  // bottom: 175px;
  left: 0;
  right: 0;
  width: 100%;
  margin: 0 auto;
  background-color: #f6f8fd;
  z-index: 10;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 5%);
}

.historyWrap {
  display: flex;
  flex-direction: row;
  height: 100%;
  max-width: 100vw;

  .historyMain {
    padding-right: 15px !important;
    padding-left: 15px !important;
  }

  .historyWrapIn {
    flex: 1;
    box-sizing: border-box;
    padding-right: 15px;
    overflow: hidden;

    &+.historyWrapIn {
      padding-right: 0;
    }

    &:only-child {
      padding: 0;
    }
  }

  .historyWrapInTrans {
    flex: 2;
  }
}

.chatTopHeader {
  position: fixed;
  top: 80px;
  width: 800px;
}

.newChat {
  width: 800px;
  height: 100%;
  margin: 0 auto;

  .newChatWrap {
    position: fixed;
    bottom: 12px;
    width: 800px;
    margin: 0 auto;
  }
}
