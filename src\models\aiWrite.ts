import { Reducer } from 'umi';

export interface AiWriteState {
  content: '';
}

export interface AiWriteType {
  namespace: 'aiWrite';
  state: AiWriteState;
  reducers: {
    setContent: Reducer<AiWriteState>;
  };
}

const AiWrite: AiWriteType = {
  namespace: 'aiWrite',

  state: {
    content: '',
  },
  reducers: {
    setContent(state, { payload }) {
      let s = {
        ...state } ;
      s.content = payload.content ;
      return s ;
    },
  },
};

export default AiWrite;
