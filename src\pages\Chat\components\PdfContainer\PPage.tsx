import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Page } from 'react-pdf';

import styles from './index.less';

interface Props {
  apage: object;
  pwidth: number;
  oxdata: object;
  istransdoc: boolean;
  hoverFreeze: boolean;
  transdatas: array;
  fileurl: string;
  filename: string;
  chatid: string;
  onTransOxItemClick: () => void;
  onOxItemHover: () => void;
  onTextSelected: () => void;
  onPMouseDown: () => void;
}

const PPage = forwardRef<HTMLDivElement, Props>((props, ref) => {
  const {
    apage,
    pwidth,
    oxdata,
    istransdoc,
    hoverFreeze,
    transdatas,
    onTransOxItemClick,
    onOxItemHover,
    onTextSelected,
    onPMouseDown,
    fileurl,
    filename,
    chatid,
  } = props;

  const [pageWidth, setPageWidth] = useState(0);
  const [pageHeight, setPageHeight] = useState(0);

  const oxItemsRef = useRef(null);

  const mouseIsDown = useRef(null);

  const lastFindIt = useRef(null);

  const selTextMode = useRef(false);

  const [transdata, setTransdata] = useState(null);

  useEffect(() => {
    if (!transdatas) return;
    setTransdata(transdatas.find((it) => it.page == apage.page));
  }, [transdatas]);

  function getWidth() {
    let rwidth = pwidth;
    if (rwidth > 1000) rwidth = 1000;
    return rwidth;
  }

  function getOxItemsRefMap() {
    if (!oxItemsRef.current) {
      oxItemsRef.current = new Map();
    }

    return oxItemsRef.current;
  }

  function getOxItemStyle(item, istrans) {
    let bbox = item.bbox;
    let ppwidth = getWidth();

    let s = {};
    s.left = (bbox[0] * 100) / pageWidth + '%';
    s.top = (bbox[1] * 100) / pageHeight + '%';
    s.width = ((bbox[2] - bbox[0]) * 100) / pageWidth + '%';
    s.height = s.minHeight = ((bbox[3] - bbox[1]) * 100) / pageHeight + '%';

    if (istrans) {
      s.fontSize = (ppwidth / pageWidth) * 12 + 'px';
      s.lineHeight = '110%';
      s.color = '#555';
    }

    return s;
  }

  function onPageMouseUp(e) {
    mouseIsDown.current = false;

    const selection = window.getSelection();
    if (selection.toString() === '') return;

    selTextMode.current = true;
    onTextSelected && onTextSelected(e, selection.toString());
  }

  function onPageMouseDown(e) {
    mouseIsDown.current = true;
    selTextMode.current = false;

    onPMouseDown && onPMouseDown(e);
  }

  function onPageMouseMove(e) {
    if (!oxdata || !oxdata.datas) return;

    //暂时冻结
    if (hoverFreeze) return;

    //文本选择模式
    if (selTextMode.current) return;

    e.stopPropagation();

    const ev = e.nativeEvent;
    let ex = ev.pageX;
    let ey = ev.pageY;

    let pageDom = e.target;
    while (pageDom.className !== 'react-pdf__Page') {
      pageDom = pageDom.parentNode;
      if (!pageDom || pageDom.tagName === 'body') return;
    }

    const s = pageDom.getBoundingClientRect();
    ex = ex - s.left;
    ey = ey - s.top;

    let ppwidth = getWidth();
    let rat = ppwidth / pageWidth;

    ex = ex / rat;
    ey = ey / rat;

    let findit = false;
    for (let i = 0; i < oxdata.datas.length; i++) {
      const ed = oxdata.datas[i].bbox;
      let ed3 = ed[3];
      if (lastFindIt.current == oxdata.datas[i]) ed3 += 50 / rat;

      if (ex > ed[0] && ex < ed[2] && ey > ed[1] && ey < ed3) {
        findit = oxdata.datas[i];
        break;
      }
    }

    if (['image'].indexOf(findit.type) > -1) findit = false;

    let oxItemsRefMap = getOxItemsRefMap();
    for (const oxItemRef of oxItemsRefMap.values()) {
      oxItemRef && (oxItemRef.style.display = '');
    }

    if (findit && !mouseIsDown.current) {
      const oxItemRef = oxItemsRefMap.get(findit);
      if (oxItemRef) {
        oxItemRef && (oxItemRef.style.display = 'block');
      }

      onOxItemHover && onOxItemHover(e, apage, findit, oxItemRef);
    } else {
      onOxItemHover && onOxItemHover(null);
    }

    lastFindIt.current = findit;
  }

  /*
 useEffect(() => {
    if(apage.page == '1'){
      dispatchInUtils({
        type: 'recentAiRead/fetchList',
        payload: { },
      });
    }
  }, []);
*/

  // const recentReadList = useSelector((state: { recentAiRead: { list: '' } }) => state.recentAiRead.list);
  function onPageRenderSuccess(e) {
    if (pageWidth) return;

    props && props.onPageRenderSucc && props.onPageRenderSucc(apage, e);

    /*
    if(!istransdoc && apage.page == 1 && pageCanvasRef.current){

      let aread = recentReadList.find(it=>it.docurl == fileurl) ;
      if(!aread || !aread.icon){
        try {
          let base64 = pageCanvasRef.current.toDataURL('image/jpeg')
          let upfile = new File([dataURItoBlob(base64)], new Date().getTime() + '.png', { type: "image/png" });
          uupload(upfile).then(dd=>{

            if(dd.code === 0 && dd.data){
              dispatchInUtils({
                type: 'recentAiRead/updateList',
                payload: {
                  docurl : fileurl ,
                  docname : filename,
                  icon : dd.data,
                  chatid : chatid,
                },
              });
            }


          });
        }catch(e){

        }
      }else{
        //只更新时间
        dispatchInUtils({
          type: 'recentAiRead/updateList',
          payload: {
            docurl : fileurl ,
            chatid : chatid,
          },
        });
      }


    }
     */

    setPageWidth(e.originalWidth);
    setPageHeight(e.originalHeight);
  }

  useImperativeHandle(ref, () => ({
    lightOxItem(oxitem) {
      let oxItemsRefMap = getOxItemsRefMap();
      for (const oxItemRef of oxItemsRefMap.values()) {
        oxItemRef && (oxItemRef.style.display = '');
      }

      if (!oxitem) return;

      const oxItemRef = oxItemsRefMap.get(oxitem);
      if (oxItemRef) {
        oxItemRef.style.display = 'block';
      }
    },
  }));

  function getOxItemTransTxt(oxindex) {
    if (transdata && transdata.datas && transdata.datas[oxindex])
      return transdata.datas[oxindex].txt;
    return '';
  }

  function getOxItemTransClass(oxindex) {
    let s = [styles.pdfContainerSelectBox, styles.pdfContainerSelectTransBox];
    if (getOxItemTransTxt(oxindex) !== '') s.push(styles.pdfContainerSelectTransedBox);

    return s.join(' ');
  }

  function clickRetryTrans() {
    props && props.retryTrans && props.retryTrans(apage);
  }

  function getShdStyle() {
    let s = {};
    s.width = getWidth();

    let rate = 1;
    let ddheight = 200;

    if (pageWidth && pageHeight) {
      rate = pageHeight / pageWidth;
    } else if (apage && apage.width && apage.height) {
      rate = apage.height / apage.width;
    }

    s.textAlign = 'center';
    s.paddingTop = 30;
    s.boxSizing = 'border-box';

    s.height = rate * s.width;
    s.backgroundColor = '#fff';
    return s;
  }

  function getPageWrapStyle() {
    let s = {};
    s.width = getWidth();

    let rate = -1;

    if (pageWidth && pageHeight) {
      rate = pageHeight / pageWidth;
    } else if (apage && apage.width && apage.height) {
      rate = apage.height / apage.width;
    }

    if (rate < 0) return s;

    s.height = rate * s.width;
    return s;
  }

  let testTransTxt =
    'It is obviously not a simple matter for a person to play multiple roles at the same time in his life, to manage well the different interpersonal relationships between his elders, juniors, and peers, his children, students, subordinates, parents, superiors, brothers and sisters, and to learn to face different identities in a mature and harmonious mode of interaction, to play different roles competently, to show appropriate coping skills everywhere, and to maintain good interpersonal relationships with others.';

  testTransTxt = '';

  if (props && props.currentPageNum > 0) {
    if (apage.page < props.currentPageNum - 5 || apage.page > props.currentPageNum + 5) {
      return <div className={styles.ppageWrap} style={getShdStyle()}></div>;
    }
  }

  let transTipTpl = '';

  if (oxdata && oxdata.loading) {
    transTipTpl = <div className={styles.ppageTransLoading}>文档解析中...</div>;
  } else {
    if (istransdoc) {
      if (transdata && transdata.loading) {
        transTipTpl = <div className={styles.ppageTransLoading}>翻译中...</div>;
      } else if (transdata && transdata.error) {
        transTipTpl = (
          <div onClick={clickRetryTrans} className={styles.ppageTransError}>
            翻译失败,点击重试
          </div>
        );
      }
    }
  }

  return (
    <div className={styles.ppageWrap} style={getPageWrapStyle()}>
      <Page
        pageNumber={apage.page}
        width={getWidth()}
        onMouseMove={onPageMouseMove}
        onMouseUp={onPageMouseUp}
        onMouseDown={onPageMouseDown}
        onRenderSuccess={onPageRenderSuccess}
        renderTextLayer={!istransdoc}
      >
        <>
          {transTipTpl}

          {oxdata &&
            oxdata.datas &&
            oxdata.datas.map((oxitem, oxindex) => {
              return istransdoc ? (
                <div
                  ref={(node) => {
                    const map = getOxItemsRefMap();
                    map.set(oxitem, node);
                  }}
                  onClick={(e) => {
                    const map = getOxItemsRefMap();
                    const oxItemRef = map.get(oxitem);
                    onTransOxItemClick(e, apage, oxitem, oxItemRef, getOxItemTransTxt(oxindex));
                  }}
                  key={oxitem.index}
                  style={getOxItemStyle(oxitem, true)}
                  className={getOxItemTransClass(oxindex)}
                >
                  {getOxItemTransTxt(oxindex)}
                  {testTransTxt}
                </div>
              ) : (
                <div
                  ref={(node) => {
                    const map = getOxItemsRefMap();
                    map.set(oxitem, node);
                  }}
                  key={oxitem.index}
                  style={getOxItemStyle(oxitem)}
                  className={styles.pdfContainerSelectBox}
                ></div>
              );
            })}
        </>
      </Page>
    </div>
  );
});

export default PPage;
