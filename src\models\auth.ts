import { getUserInfoApi } from '@/services/auth';
import { Effect, Reducer } from 'umi';

export interface UserType {
  avatar: string;
  deptId: number;
  id: number;
  name: string;
  nickname: string;
  username: string;
  mobile: string;
  email: string;
}

export interface AuthModelState {
  user: UserType | null;
}

export interface AuthModelType {
  namespace: 'auth';
  state: AuthModelState;
  effects: {
    fetchUser: Effect;
  };
  reducers: {
    save: Reducer<AuthModelState>;
    reset: Reducer<AuthModelState>;
  };
}

const AuthModel: AuthModelType = {
  namespace: 'auth',

  state: {
    user: null,
  },

  effects: {
    *fetchUser(_, { call, put }): Generator<any> {
      // 调用 API 获取用户信息
      const response: any = yield call(getUserInfoApi);
      // 更新状态
      yield put({
        type: 'save',
        payload: { user: response?.data?.user },
      });
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    reset() {
      return {
        user: null,
      };
    },
  },
};

export default AuthModel;
