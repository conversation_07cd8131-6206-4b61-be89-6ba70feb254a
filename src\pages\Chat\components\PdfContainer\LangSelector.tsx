import React,{ useRef,useState,useEffect } from 'react';
import styles from './index.less';

import { Popover,Switch } from '@douyinfe/semi-ui';
import { IconChevronRight } from '@douyinfe/semi-icons';

import LangInSelector from './LangInSelector.tsx';


interface Props {
  showOriBtn:boolean;
  onShowOriPdfChange: ()=>void;
  onLangsChange: ()=>void;
  showOriPdf:boolean;
  sourceLang:object;
  targetLang:object;
}

const LangSelector: React.FC<Props> = ({
  showOriBtn=true,
  onShowOriPdfChange,
  onLangsChange,
  showOriPdf,
  sourceLang,
  targetLang,
}: Props) => {

  const [visibleSourceLangInSelector, setVisibleSourceLangInSelector] = useState(false);
  const [visibleTargetLangInSelector, setVisibleTargetLangInSelector] = useState(false);


  function onTargetLangInSelectorSelected(langItem,e){
    hideLangInSelector();
    onLangsChange && onLangsChange({
      type:'target',
      data:langItem,
    });
  }

  function onSourceLangInSelectorSelected(langItem,e){
    hideLangInSelector();
    onLangsChange && onLangsChange({
      type:'source',
      data:langItem,
    });
  }

  function hideLangInSelector(){
    setVisibleSourceLangInSelector(false);
    setVisibleTargetLangInSelector(false);
  };


  return (
	<div className={styles.langSelectorWrap}>
    {showOriBtn ? (<div className={styles.langSelectorTitle}>
			<div className={styles.flex1}>
				显示原文
			</div>
			<Switch checked={showOriPdf} onChange={
				(v, e) => onShowOriPdfChange(v)
			}></Switch>
		</div>) : ''}

		<div className={styles.langSelectorItem}>
			<div className={styles.title}>
				源语言
			</div>

      <Popover
				content={
          <LangInSelector onSelected={onSourceLangInSelectorSelected} selectedLang={sourceLang} uAutoItem={true}></LangInSelector>
        }
        visible={visibleSourceLangInSelector}
        onVisibleChange={setVisibleSourceLangInSelector}
				position="right"
        stopPropagation={true}
				trigger="click">

				<div className={styles.toin}>
					<div className={styles.flex1}>
						{sourceLang.name}
					</div>
					<IconChevronRight style={{ color: '#999' }} />
				</div>
			</Popover>
		</div>

		<div className={styles.langSelectorItem}>
			<div className={styles.title}>
				目标语言
			</div>

			<Popover
				content={
					<LangInSelector onSelected={onTargetLangInSelectorSelected} selectedLang={targetLang}></LangInSelector>
				}
        visible={visibleTargetLangInSelector}
        onVisibleChange={setVisibleTargetLangInSelector}
				position="right"
        stopPropagation={true}
				trigger="click">

				<div className={styles.toin}>
					<div className={styles.flex1}>
						{targetLang.name}
					</div>
					<IconChevronRight style={{ color: '#999' }} />
				</div>
			</Popover>

		</div>

	</div>
  );
};

export default LangSelector;
