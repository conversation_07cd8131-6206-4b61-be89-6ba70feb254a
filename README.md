## 项目简介

中国电建北京院 AI 前端项目

原型地址：[prd](https://mastergo.com/prototyping/153496264370991?fileOpenFrom=project&page_id=M&shareId=153496264370991)

UI 地址：[UI](https://mastergo.com/files/project/154470096226064?org_id=132475316507020)

任务排期：[排期](https://docs.bhidi.com/weboffice/l/sfjwryyEH0ByQ)

### 主要功能

- AI服务平台

### 技术栈

- React
- React-router
- Umi
- Semi Design

### 构建工具
#### yarn
#### node版本 18以上

### 步骤
1. 安装 yarn
2. 进入项目根目录
3. 安装依赖 yarn install
4. 运行 yarn dev

