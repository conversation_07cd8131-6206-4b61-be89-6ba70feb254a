import { ReactComponent as BookGray } from '@/assets/chat/book-gray.svg';
import { ReactComponent as Book } from '@/assets/chat/book.svg';
import { ReactComponent as ComparisonGray } from '@/assets/chat/comparison-gray.svg';
import { ReactComponent as Comparison } from '@/assets/chat/comparison.svg';
import { ReactComponent as DesignGray } from '@/assets/chat/design-gray.svg';
import { ReactComponent as Design } from '@/assets/chat/design.svg';
import { ReactComponent as ReferenceGray } from '@/assets/chat/reference-gray.svg';
import { ReactComponent as Reference } from '@/assets/chat/reference.svg';
import { SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS } from '@/config';
import { FILE_CALCULATE_THE_AUDIT_COUNT, STANDARD_FILES_COUNT } from '@/config/chat';
import {
  BENCHMARK_FILES_KEY,
  CALCULATION_BOOK_KEY,
  COMPARE_FILE_KEY,
  DESIGN_STANDARD_KEY,
} from '@/config/smart';
import { ReferenceItem } from '@/models/chat';
import UploadComponent, { UploadFn } from '@/pages/Chat/components/Upload';
import { SmartCurrTabEnum, SmartFileStatus } from '@/pages/Chat/types';
import { Button, Toast, Tooltip } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useSelector } from 'umi';
import styles from '../index.less';

interface SmartUploadItemProps {
  appCode: string;
  onSuccess: (resp: any, file: File, fileType: string) => void;
  uploadKey: number;
  uploadProgress: (percent: number, file: File, fileTag?: string) => void;
  beforeRichInputSend: () => boolean;
  smarButtonQuote?: boolean;
}

const SmartUploadItem = forwardRef((props: SmartUploadItemProps, ref) => {
  const { appCode, onSuccess, uploadKey, uploadProgress, beforeRichInputSend, smarButtonQuote } =
    props;
  const standardRef = useRef<UploadFn>(null);
  const calculationSheetRef = useRef<UploadFn>(null);
  const comparisonReportRef = useRef<UploadFn>(null);
  const standardFilesRef = useRef<UploadFn>(null);
  // 从Redux中获取引用项状态，而不是本地状态
  const referenceItems = useSelector(
    (state: { chat: { references: ReferenceItem[] } }) => state.chat.references,
  );

  // 当引用项发生变化时，重置文件上传状态
  useEffect(() => {
    if (referenceItems.length > 0) {
      setIsFileStandard({ status: true, uid: '', percent: 0 });
      setIsFileCalculationSheet({ status: true, uid: '', percent: 0 });
      setIsStandardFiles({ status: true, uid: '', percent: 0 });
    } else {
      setIsFileStandard({ status: false, uid: '', percent: 0 });
      setIsFileCalculationSheet({ status: false, uid: '', percent: 0 });
      setIsStandardFiles({ status: false, uid: '', percent: 0 });
    }
  }, [referenceItems]);

  // 设计标准和计算书是否上传成功
  const [isFileStandard, setIsFileStandard] = useState<SmartFileStatus>({
    status: false,
    uid: '',
    percent: 0,
  });
  const [isFileCalculationSheet, setIsFileCalculationSheet] = useState<SmartFileStatus>({
    status: false,
    uid: '',
    percent: 0,
  });

  const [isFileComparisonReport, setIsFileComparisonReport] = useState<SmartFileStatus[]>([]);
  const [isStandardFiles, setIsStandardFiles] = useState<SmartFileStatus>({
    status: false,
    uid: '',
    percent: 0,
  });

  const handleClearFileType = (file: any, uid: string) => {
    // 如果文件列表为空，重置状态
    if (isFileCalculationSheet.status && isFileCalculationSheet.uid === uid) {
      setIsFileCalculationSheet({ status: false, uid: '', percent: 0 });
      calculationSheetRef.current?.removeFile(file);
    }
    if (isFileStandard.status && isFileStandard.uid === uid) {
      setIsFileStandard({ status: false, uid: '', percent: 0 });
      standardRef.current?.removeFile(file);
    }

    if (isFileComparisonReport.length) {
      const newFileComparisonReport = isFileComparisonReport.filter((item) => item.uid !== uid);
      setIsFileComparisonReport(newFileComparisonReport);
      comparisonReportRef.current?.removeFile(file);
    }
    if (isStandardFiles.status && isStandardFiles.uid === uid) {
      setIsStandardFiles({ status: false, uid: '', percent: 0 });
      standardFilesRef.current?.removeFile(file);
    }
  };

  const handleClearFileType1 = (fileList: any) => {
    fileList.forEach((item: any) => {
      if (item.fileTags?.includes(DESIGN_STANDARD_KEY)) {
        calculationSheetRef.current?.removeFile(item, true);
      } else if (item.fileTags?.includes(CALCULATION_BOOK_KEY)) {
        standardRef.current?.removeFile(item, true);
      } else if (item.fileTags?.includes(COMPARE_FILE_KEY)) {
        comparisonReportRef.current?.removeFile(item, true);
      } else if (item.fileTags?.includes(BENCHMARK_FILES_KEY)) {
        standardFilesRef.current?.removeFile(item, true);
      }
    });
  };

  /**
   * 检查单个文件上传是否未完成
   */
  const isFileUploadIncomplete = (fileStatus: SmartFileStatus): boolean => {
    return fileStatus.percent < 100; // 只需检查进度是否未达到100%
  };

  /**
   * 检查有一个文件上传成功就可以
   */
  const isFileUploadAllSuccess = (fileStatusList: SmartFileStatus[]): boolean => {
    return fileStatusList.some((status) => status.status && status.percent === 100);
  };

  const handleSubmitSmart = () => {
    // 先检验文件上传状态
    if (!beforeRichInputSend()) {
      return true;
    }

    // 当前选项卡为 "计算审核" 时的校验逻辑
    if (appCode === SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.engin - 1]) {
      if (
        isFileUploadIncomplete(isFileStandard) || // 标准文件未完成上传
        isFileUploadIncomplete(isFileCalculationSheet) // 计算书未完成上传
      ) {
        Toast.info({
          content: '请分别各上传一个文件',
          duration: 3,
        });
        return true;
      }
      return false;
    }

    // 当前选项卡为 "内容对比" 时的校验逻辑
    if (appCode === SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_compare - 1]) {
      if (
        !isFileUploadAllSuccess(isFileComparisonReport) || // 一个文件上传成功就可以
        isFileUploadIncomplete(isStandardFiles) // 标准文件未完成上传
      ) {
        Toast.info({
          content: '请您至少上传1个比对文件以及1个基准文件',
          duration: 3,
        });
        return true;
      }
      return false;
    }
  };

  const handleTabClearStatus = () => {
    setIsFileCalculationSheet({ status: false, uid: '', percent: 0 });
    setIsFileStandard({ status: false, uid: '', percent: 0 });
    setIsFileComparisonReport([]);
    setIsStandardFiles({ status: false, uid: '', percent: 0 });
  };

  useImperativeHandle(ref, () => {
    return { handleClearFileType, handleSubmitSmart, handleTabClearStatus, handleClearFileType1 };
  });

  // 计算书审核
  const calculationSheetReview = () => {
    return (
      <div className="flex items-center">
        <UploadComponent
          key={`file-upload-standard`}
          ref={standardRef}
          accept=".pdf,.txt,.docx,.doc"
          onExceed={() => Toast.info(`最多只能上传${FILE_CALCULATE_THE_AUDIT_COUNT}个文件`)}
          limit={smarButtonQuote ? 0 : FILE_CALCULATE_THE_AUDIT_COUNT}
          onProgress={(percent, file) => {
            setIsFileStandard({
              status: true,
              uid: (file as File & { uid: string }).uid,
              percent: percent,
            });
            uploadProgress(percent, file, DESIGN_STANDARD_KEY);
          }}
          onSuccess={(resp: any, file: any) => onSuccess(resp, file, 'file')}
          propsClassName={styles.uploadFile}
          UploadButton={
            <Tooltip content="最多上传1个文件，支持格式：pdf、txt、docx、doc，单个文件大小不超过10MB">
              <Button
                theme="borderless"
                type="tertiary"
                className={styles.onlineQueryBtn}
                disabled={smarButtonQuote || isFileStandard.status}
                icon={smarButtonQuote || isFileStandard.status ? <DesignGray /> : <Design />}
                onClick={(e) => e.currentTarget.blur()}
              >
                设计标准
              </Button>
            </Tooltip>
          }
        />

        <UploadComponent
          key={`file-upload-book`}
          ref={calculationSheetRef}
          accept=".pdf,.txt,.docx,.doc"
          onExceed={() => Toast.info(`最多只能上传${FILE_CALCULATE_THE_AUDIT_COUNT}个文件`)}
          limit={smarButtonQuote ? 0 : FILE_CALCULATE_THE_AUDIT_COUNT}
          onProgress={(percent, file) => {
            uploadProgress(percent, file, CALCULATION_BOOK_KEY);
            setIsFileCalculationSheet({
              status: true,
              uid: (file as File & { uid: string }).uid,
              percent: percent,
            });
          }}
          onSuccess={(resp: any, file: any) => onSuccess(resp, file, 'file')}
          propsClassName={styles.uploadFile}
          UploadButton={
            <Tooltip content="最多上传1个文件，支持格式：pdf、txt、docx、doc，单个文件大小不超过10MB">
              <Button
                theme="borderless"
                type="tertiary"
                className={classNames(styles.onlineQueryBtn, 'ml-[17px]')}
                disabled={smarButtonQuote || isFileCalculationSheet.status}
                icon={smarButtonQuote || isFileCalculationSheet.status ? <BookGray /> : <Book />}
                onClick={(e) => e.currentTarget.blur()}
              >
                计算书
              </Button>
            </Tooltip>
          }
        />
      </div>
    );
  };

  //报告内容对比
  const reportContentComparison = () => {
    return (
      <div className="flex items-center">
        <UploadComponent
          key={`file-upload-${uploadKey}`}
          ref={standardFilesRef}
          accept=".pdf,.txt,.docx,.doc"
          onExceed={() => Toast.info(`最多只能上传${FILE_CALCULATE_THE_AUDIT_COUNT}个文件`)}
          limit={smarButtonQuote ? 0 : FILE_CALCULATE_THE_AUDIT_COUNT}
          onProgress={(percent, file) => {
            uploadProgress(percent, file, COMPARE_FILE_KEY);
            setIsStandardFiles({
              status: true,
              uid: (file as File & { uid: string }).uid,
              percent: percent,
            });
          }}
          onSuccess={(resp: any, file: any) => onSuccess(resp, file, 'file')}
          propsClassName={styles.uploadFile}
          UploadButton={
            <Tooltip
              content={`最多上传${FILE_CALCULATE_THE_AUDIT_COUNT}个文件，支持格式：pdf、txt、docx、doc，单个文件大小不超过10MB`}
            >
              <Button
                theme="borderless"
                type="tertiary"
                className={styles.onlineQueryBtn}
                disabled={smarButtonQuote || isStandardFiles.status}
                icon={
                  smarButtonQuote || isStandardFiles.status ? <ComparisonGray /> : <Comparison />
                }
                onClick={(e) => e.currentTarget.blur()}
              >
                比对文件
              </Button>
            </Tooltip>
          }
        />

        <UploadComponent
          key={`file-upload-standard`}
          ref={comparisonReportRef}
          accept=".pdf,.txt,.docx,.doc"
          limit={smarButtonQuote ? 0 : STANDARD_FILES_COUNT}
          multiple={true}
          onExceed={() => Toast.info(`最多只能上传${STANDARD_FILES_COUNT}个文件`)}
          onProgress={(percent, file) => {
            uploadProgress(percent, file, BENCHMARK_FILES_KEY);
            setIsFileComparisonReport((prev) => {
              const existingFile = prev.find(
                (item) => item.uid === (file as File & { uid: string }).uid,
              );
              if (existingFile) {
                return prev.map((item) =>
                  item.uid === (file as File & { uid: string }).uid ? { ...item, percent } : item,
                );
              } else {
                return [
                  ...prev,
                  { status: true, uid: (file as File & { uid: string }).uid, percent },
                ];
              }
            });
          }}
          onSuccess={(resp: any, file: any) => onSuccess(resp, file, 'file')}
          propsClassName={styles.uploadFile}
          UploadButton={
            <Tooltip
              content={`最多上传${STANDARD_FILES_COUNT}个文件，支持格式：pdf、txt、docx、doc，单个文件大小不超过10MB`}
            >
              <Button
                theme="borderless"
                type="tertiary"
                className={classNames(styles.onlineQueryBtn, 'ml-[17px]')}
                disabled={smarButtonQuote || isFileComparisonReport.length >= STANDARD_FILES_COUNT}
                icon={
                  smarButtonQuote || isFileComparisonReport.length >= STANDARD_FILES_COUNT ? (
                    <ReferenceGray />
                  ) : (
                    <Reference />
                  )
                }
                onClick={(e) => e.currentTarget.blur()}
              >
                基准文件
              </Button>
            </Tooltip>
          }
        />
      </div>
    );
  };

  return (
    <>
      {appCode === SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.engin - 1]
        ? calculationSheetReview()
        : appCode === SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS[SmartCurrTabEnum.report_compare - 1]
        ? reportContentComparison()
        : null}
    </>
  );
});

export default SmartUploadItem;
