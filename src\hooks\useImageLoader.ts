import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// 增加缓存大小限制，避免内存泄漏
const MAX_CACHE_SIZE = 500;
const loadedImageCache = new Map<string, boolean>();

// 清理缓存的函数
const cleanupCache = () => {
  if (loadedImageCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(loadedImageCache.entries());
    const toDelete = entries.slice(0, Math.floor(MAX_CACHE_SIZE * 0.3));
    toDelete.forEach(([key]) => loadedImageCache.delete(key));
  }
};

const useImageLoader = (url: string) => {
  const [isLoaded, setIsLoaded] = useState(loadedImageCache.has(url));
  const [isError, setIsError] = useState(false);
  const imgRef = useRef<HTMLImageElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [isInView, setIsInView] = useState(false);
  const loadingRef = useRef(false);

  const memoizedUrl = useMemo(() => url, [url]);

  useEffect(() => {
    if (!imgRef.current || isLoaded) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      { threshold: 0.01, rootMargin: '500px 0px' },
    );

    observerRef.current.observe(imgRef.current);
    return () => observerRef.current?.disconnect();
  }, [isLoaded]);

  const loadImage = useCallback(() => {
    if (loadingRef.current || isLoaded || !memoizedUrl) return;

    if (loadedImageCache.has(memoizedUrl)) {
      setIsLoaded(true);
      return;
    }

    loadingRef.current = true;
    const img = new Image();
    img.src = memoizedUrl;
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      loadedImageCache.set(memoizedUrl, true);
      cleanupCache();
      setIsLoaded(true);
      setIsError(false);
      loadingRef.current = false;
    };

    img.onerror = () => {
      setIsError(true);
      setIsLoaded(true);
      loadingRef.current = false;
    };

    return () => {
      img.onload = null;
      img.onerror = null;
      loadingRef.current = false;
    };
  }, [memoizedUrl, isLoaded]);

  useEffect(() => {
    if (!isInView || isLoaded || !memoizedUrl) return;

    const cleanup = loadImage();
    return cleanup;
  }, [memoizedUrl, isInView, isLoaded, loadImage]);

  return { imgRef, isLoaded, isError };
};

export default useImageLoader;
