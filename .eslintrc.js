module.exports = {
  extends: [
    // Umi 4 默认的 ESLint 配置
    require.resolve('umi/eslint'),

    // 追加 Prettier 配置（必须放在最后）
    'plugin:prettier/recommended',
  ],
  // 可选：自定义规则覆盖
  rules: {
    'prettier/prettier': 'warn', // 将 Prettier 不符合项标记为警告
    '@typescript-eslint/no-use-before-define': [
      'error',
      {
        functions: false, // 允许函数在定义之前使用
        variables: false, // 允许变量在定义之前使用
        classes: true, // 禁止类在定义之前使用
        enums: true, // 禁止枚举在定义之前使用
      },
    ],
  },
};
