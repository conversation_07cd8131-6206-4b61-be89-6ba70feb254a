import { uploadApi } from '@/services/chat';
import { getAccessToken, getTenantId } from '@/utils';
import { Toast, Upload } from '@douyinfe/semi-ui';
import { FileItem, UploadProps } from '@douyinfe/semi-ui/lib/es/upload';
import classNames from 'classnames';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './index.less';

const uploadUrl = process.env.API_URL + uploadApi;

const uploadData = {
  path: 'ai/portal/',
  configName: 'aifile',
};

const headers = {
  'tenant-id': getTenantId(),
  authorization: 'Bearer ' + getAccessToken(),
};

// 定义暴露方法的类型
export interface UploadFn {
  /**
   * 移除文件
   * @returns
   */
  removeFile: (file: FileItem, isEmpty?: boolean) => void;
}

export interface UploadCompProps {
  UploadButton: JSX.Element;
}

const UploadComponent = forwardRef<
  UploadFn,
  UploadCompProps &
    Pick<
      UploadProps,
      'onExceed' | 'onSuccess' | 'accept' | 'disabled' | 'onProgress' | 'afterUpload' | 'limit'
    > & {
      isIcon?: boolean;
      propsClassName?: string;
      // 是否可多选上传
      multiple?: boolean;
      // 是否只上传一个文件
      isUploadOne?: boolean;
      fileSize?: number;
    }
>((Props, ref) => {
  //
  const [fileList, setFileList] = useState<any[]>([]);
  const uploadRef = useRef<Upload>(null);
  const [disabled, setDisabled] = useState(false);
  const [maxSize, setMaxSize] = useState(10);

  useEffect(() => {
    if (!Props.limit) {
      setDisabled(true);
    } else {
      setDisabled(false);
    }
  }, [Props.limit]);

  useEffect(() => {
    if (Props.fileSize) {
      setMaxSize(Props.fileSize);
    }
  }, [Props.fileSize]);

  const onChange = ({ fileList }: { fileList: FileItem[]; currentFile: File }) => {
    // 过滤验证失败的文件
    let length = fileList.length;
    if (Props.limit === 0) {
      setDisabled(true);
    } else if (length === Props.limit && !Props.isIcon) {
      setDisabled(true);
    } else {
      setDisabled(false);
    }
    let newFileList = fileList.filter((item: FileItem) => item.status !== 'validateFail'); // spread to get new array
    setFileList(newFileList);
  };

  // 通用的文件大小超限错误处理函数
  const handleFileSizeError = (file: FileItem) => {
    Toast.error({
      content: `上传内容"${file.name}"超过大小限制，请上传${maxSize}MB以内的文件`,
      duration: 3,
    });
    setTimeout(() => {
      removeFile(file);
    });
  };

  /**
   * 删除文件
   * @param file
   */
  const removeFile = (file: FileItem, isEmpty?: boolean) => {
    if (uploadRef.current) {
      if (isEmpty) {
        setFileList([]);
        onChange({ fileList: [], currentFile: file.fileInstance as File });
      } else {
        const newFileList = fileList.filter((item) => item.uid !== file.uid);
        setFileList(newFileList);
        onChange({ fileList: newFileList, currentFile: file.fileInstance as File });
      }
      uploadRef.current?.remove(file);
    }
  };

  useImperativeHandle(ref, () => ({
    /**
     * 删除文件
     * @param file
     */
    removeFile,
  }));

  return (
    <>
      <div className={classNames(styles.uploadButton, Props.propsClassName)}>
        <Upload
          ref={uploadRef}
          action={uploadUrl}
          fileList={fileList}
          headers={headers}
          data={uploadData}
          fileName="file"
          // limit={MAX_FILE_COUNT}
          maxSize={maxSize * 1024}
          showUploadList={false}
          multiple={!!Props.multiple}
          {...(Props as any)}
          onChange={onChange}
          disabled={disabled}
          onSizeError={handleFileSizeError}
          // 执行步骤 transformFile => beforeUpload=> onChange
          // Props.isUploadOne 处理是否只需要上传一个文件
          transformFile={(fileInstance) => {
            if (Props.isUploadOne) {
              return fileList[0].fileInstance;
            }
            return fileInstance;
          }}
          beforeUpload={() => {
            if (Props.isUploadOne) {
              if (fileList[0].percent !== 100) {
                return {
                  // 是否从fileList中移除该文件，默认为false
                  autoRemove: false,
                  fileInstance: fileList[0].fileInstance,
                  // 是否应该继续上传
                  shouldUpload: false,
                };
              }
              return false;
            }
            return true;
          }}
        >
          {Props.UploadButton}
        </Upload>
      </div>
    </>
  );
});

export default UploadComponent;
