import request from '@/utils/request';

enum Api {
  Login = '/system/auth/login',
  RefreshToken = '/system/auth/refresh-token?refreshToken=',
  LoginOut = '/system/auth/logout',
  GetUserInfo = '/',
  GetTenantIdByCode = '/system/tenant/get-id-by-code',
}

/**
 * 获取租户id
 * @param code
 * @returns
 */
export function getTenantIdByCode(code: string) {
  return request(Api.GetTenantIdByCode, {
    method: 'GET',
    params: { code },
  });
}

interface LoginParams {
  username: string;
  password: string;
}
/**
 * 登录
 * @param data
 * @returns
 */
export function login(data: LoginParams) {
  return request(Api.Login, {
    method: 'POST',
    data,
  });
}
