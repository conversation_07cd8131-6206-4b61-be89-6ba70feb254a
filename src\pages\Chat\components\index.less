.chatLayout {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .chatLeft {
    width: 240px;
    height: 100%;
    background: #f8faff;
  }

  .chatRight {
    flex: 1;
    height: 100%;
    background: #fff;
    position: relative;
  }
}

.streamError {
  padding: 10px 16px;
  font-size: 14px;
  color: var(--semi-color-text-0);
}

.uploadFileStatus {
  white-space: nowrap
}

.onlineQueryBtn {
  border-radius: 8px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #ebeef2 !important;
  box-sizing: border-box;

  &:global(.semi-button-tertiary.semi-button-borderless) {
    color: #000;
  }

  &:global(.semi-button-primary.semi-button-borderless) {
    border: none;
    background: rgba(0, 91, 248, 8%);
  }

  :global(.semi-button-content-right) {
    margin-left: 4px;
    font-weight: normal;
  }
}

.chatBottomSlot {
  font-size: 14px;
  color: #ccc;
  text-align: center;
  margin-top: 12px;
  line-height: 22px;
  margin-bottom: 12px;
}

.hintBox {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 8px;
  background: #F6F8FD;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  color: #000;
  padding: 10px 16px;
  width: fit-content;
}

.chatInput {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 16px;
  border: 1px solid #ededed;
  box-shadow: 0 4px 20px 0 rgba(222, 229, 241, 57%);
  overflow: hidden;
  background: #fff;
}

.chatInputTitle {
  position: relative;
  width: 100%;
  height: 48px;
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  color: #000;
  background: #f6f8fd;
  border-radius: 8px 8px 0 0;
  padding: 12px;
  transition: all 0.3s;

  span {
    line-height: 48px;
  }
}

.chatInputClose {
  font-size: 16px;
  cursor: pointer;
}

.thinkingTitle {
  font-size: 14px;
  color: #ccc;
  text-align: center;
  margin-top: 12px;
  line-height: 22px;
}

.reportValidTable {
  padding: 10px 16px;
  font-size: 14px;

  :global(.title) {
    font-size: 16px;
    margin: 12px 0;
  }
}

.reasoningContent {
  font-size: 12px;
  color: #3d3d3d;
  margin: 12px 0;
  border-left: 1px solid #e5e5e5;
  padding-left: 10px;
  line-height: 20px;
}

.reasoningContentPre {
  white-space: break-spaces;
}

.isReasoningLoading {
  display: flex;
  align-items: center;
  line-height: 0;

  .isReasoningLoadingText{
    font-size: 14px;
    color: #999;
    margin-left: 4px;
  }

  :global(.semi-spin-wrapper) {
    color: #999;
  }
}

.errorContent {
  border-radius: 8px;
  background: #f6f8fd;
}

.uploadButton {
  width: 24px;
  height: 24px;
}

.uploadFile {
  width: auto;
  height: auto;
}

.fileContent {
  cursor: pointer;
}

.chatTopSlot p {
  font-size: 32px;
  font-weight: 600;
  line-height: 40px;
  color: #000;
  text-align: center;
  margin-bottom: 32px;
}

.swiperData {
  width: 100%;
  max-width: 800px;
  height: 180px;
  margin: 0 auto;
  margin-bottom: 24px;
  border-radius: 16px;
  position: relative;

    .swiperItemContentL {
      position: absolute;
      left: 24px;
      top: 32px;

      .swiperItemContentLTime {
        height: 48px;
        font-family: "DIN Alternate";
        font-size: 40px;
        font-weight: bold;
        line-height: 48px;
        letter-spacing: normal;
        color: #FFF;
        text-shadow: 0 4px 2px rgba(0, 0, 0, 10%);
      }

      .swiperItemContentLDate {
        height: 24px;
        font-family: "PingFang SC";
        font-size: 16px;
        font-weight: 500;
        text-shadow: 0 2px 2px rgba(0, 0, 0, 30%);
        line-height: 24px;
        letter-spacing: normal;
        color: #FFF;
      }
    }

    .swiperItemContentR {
      position: absolute;
      right: 30px;
      top: 40px;

      .swiperItemContentRWeather {
        display: flex;

        .swiperItemContentRWeatherTemp {
          font-family: "PingFang SC";
          font-size: 40px;
          font-weight: 500;
          line-height: 40px;
          letter-spacing: normal;
          color: #FFF;
          text-shadow: 0 2px 2px rgba(0, 0, 0, 10%);
          margin-right: 12px;
        }

        .swiperItemContentRWeatherIcon {
          width: 40px;
          height: 40px;
        }
      }

      .swiperItemContentRWeatherCity {
        height: 24px;
        font-family: "PingFang SC";
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: normal;
        color: #FFF;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 30%);
      }
    }


  .swiperItemBotText {
    width: 100%;
    background: rgba(0, 0, 0, 60%);
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 500;
    letter-spacing: normal;
    color: #FFF;
    position: absolute;
    bottom: 0;
    padding: 8px 16px 10px;
  }

  .swiperCarosel{
    width: 100%;
    height: 100%;
    border-radius: 16px;
    position: absolute;
    top: 0;
    z-index: -1;
  }
}

.appEntrance {
  width: 800px;
  height: 42px;
  margin: 0 auto 16px;

  :global(.semi-button) {
    height: 42px;
    border-radius: 24px;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid #ebeef2;
    margin-right: 12px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  :global(.semi-button-content-right) {
    font-size: 14px;
    font-weight: normal;
    color: #000;
    margin-left: 4px;
  }
}

.dropdown {
  width: 172px;
  border-radius: 4px;
  background: #FFF;
  box-shadow: 0 2px 9px 0 rgba(187, 187, 187, 50%);
}

.dropdownMenu {
  padding: 4px;
}

.dropdownItem {
  width: 164px;
  height: 32px;
  border-radius: 4px;
  background: #FFF;
  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: #000;
}

.dropdownItem:hover {
  background: #F5F7FC;
}

// 聊天框左侧盒子
.sidebar {
  width: 100%;
  height: 100%;

  &__top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;
  }
}

.greeting {
  position: relative;
  font-weight: 600;
  text-align: center;
  width: fit-content;
  margin: 0 auto;
}

.greeting-animation {
  &::after {
    animation: after 0.2s linear both;
    animation-delay: 0.3s;
    background: #fff;
    content: '';
    height: 100%;
    left: 0;
    position: absolute;
    width: 100%;
  }

  &::before {
    animation: before 0.2s linear both;
    animation-delay: 0.3s;
    content: '';
    height: 100%;
    left: -32px;
    mask-image: linear-gradient(270deg, #000, transparent);
    position: absolute;
    width: 32px;
  }
}

@media (min-width: 600px) {
  .greeting {
    font-size: 32px;
    height: 45px;
    margin-bottom: 32px;
  }
}

@media (min-width: 400px) and (max-width: 599px) {
  .greeting {
    font-size: 24px;
    height: 33.5px;
    margin-bottom: 28px;
  }
}

@media (max-width: 399px) {
  .greeting {
    font-size: 20px;
    height: 28px;
    margin-bottom: 20px;
  }
}

@keyframes before {
  0% {
    left: -32px;
  }

  to {
    left: 100%;
  }
}

@keyframes after {
  0% {
    left: 0;
  }

  to {
    left: calc(100% + 32px);
  }
}

.chatInputUploadFile {
  display: flex;
}

.uploadFileList {
  width: 100%;
}

.customFileShowList {
  position: relative;
  display: flex;
  padding: 12px;
}

.customFileList {
  display: flex;
  max-height: 56px;
  flex-wrap: nowrap;
  gap: 8px;
  // margin-bottom: 12px;
  position: relative;
  transition: all 0.3s;
}

.customFileLeftArrow {
  position: absolute;
  display: flex;
  align-items: center;
  left: -12px;
  height: 56px;
  justify-content: center;
  width: 48px;
  background: linear-gradient(90deg, #fff 66%, rgba(255, 255, 255, 0%) 98%);
  z-index: 50;
  cursor: pointer;

  span {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f6f8fd;
    text-align: center;
    cursor: pointer;
  }
}

.customFileRightArrow {
  position: absolute;
  right: -12px;
  height: 56px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(270deg, #fff 66%, rgba(255, 255, 255, 0%) 98%);
  cursor: pointer;

  span {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f6f8fd;
    text-align: center;
    cursor: pointer;
  }
}

.customFileArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  background: linear-gradient(270deg, #fff 66%, rgba(255, 255, 255, 0%) 98%);

  span {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f6f8fd;
    text-align: center;
    cursor: pointer;
  }
}

.fileProgress {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: var(--upload-progress, 100%);
    background-color: rgba(var(--semi-blue-4), 0.1);
    transition: width 0.3s ease;
    z-index: 1;
    border-radius: 4px 0 0 4px;
  }

  &[style*="--upload-progress: 100%"]::before {
    display: none;
  }
}

.customFileItem {
  display: flex;
  align-items: flex-start;
  align-items: center;
  background-color: #f6f8fd;
  border-radius: 4px;
  padding: 8px 12px;
  position: relative;
  cursor: pointer;
  max-height: 56px;
  width: 200px;
  flex: 0 0 200px;
}

.fileIcon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 4px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;

  svg {
    width: 32px;
    height: 32px;
    color: #fff;
  }
}

.fileIconInner {
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.fileInfo {
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 1;
}

.fileNameRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  position: relative;
}

.fileName {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.fileDelete {
  position: absolute;
  color: white;
  right: -18px;
  top: -14px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
  z-index: 1;

  img {
    width: 12px;
    height: 12px;
  }
}

.customFileItem:hover .fileDelete {
  opacity: 1;
}

.fileMetaRow {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.fileType,
.fileSize,
.fileWordCount {
  color: #999;
}

.fileDot {
  margin: 0 4px;
  color: #ccc;
}

.chatInputTextAreawrapper{
  position: relative;
}

.chatInputTextArea {
  padding: 12px;
  border: none !important;

  &:global(.semi-input-textarea-wrapper) {
    background-color: transparent !important;
  }

  &:global(.semi-input-textarea-borderless) {
    background: transparent;
  }

  :global(.semi-input-textarea) {
    font-size: 16px;
    outline: none;
  }

  textarea {
    height: 30px;
    caret-color: #4876F9;

    &:hover {
      border: none !important;
      background-color: #fff !important;
    }

    &:focus {
      border: none !important;
      background-color: #fff !important;
    }
  }




  &~.chatInputTextAreaPlaceholder {
    position: absolute;
    top: 12px;
    left: 12px;
    pointer-events: none;

    p {
      color: #CCC;
      font-family: "Source Han Sans CN";
      font-size: 16px;
      line-height: 20px;
      letter-spacing: normal;
    }

    span {
      color: #005BF8
    }
  }

  &:placeholder-shown~.chatInputTextAreaPlaceholder {
    display: block !important;
  }

  &:has(:placeholder-shown)~.chatInputTextAreaPlaceholder {
    display: none !important;
  }


  &:hover {
    border: none !important;
    background-color: #fff !important;
  }

  &:focus {
    border: none !important;
    background-color: #fff !important;
  }
}

.fileImagePreview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.imageFileItem {
  position: relative;
  cursor: pointer;
  width: 56px;
  height: 56px;
  border-radius: 8px;
  flex: 0 0 56px;

  img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
    overflow: hidden;
    object-fit: contain;
  }

  .imgFileProgress {
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
  }
}

.imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}

.imagePreview {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.imageDelete {
  position: absolute;
  right: -6px;
  top: -6px;
  width: 16px;
  height: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
  border-radius: 50%;
  z-index: 1;

  img {
    width: 12px;
    height: 12px;
  }
}

.imageFileItem:hover .imageDelete {
  opacity: 1;
}

.startPoint {
  height: 0;
  overflow: visible;
  position: relative;
  user-select: none;
  width: 0;

  .placeholder {
    left: 0;
    opacity: 0.3;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: max-content;
  }
}

.chatDivInputTextArea {
  outline: none;
  padding: 12px;
  caret-color: #4876F9;


  * {
    outline: none;
  }
}

.inputContainer {
  background: rgb(1 61 176 / 6%);
  border-radius: 10px;
  color: #06f;
  color: var(--s-color-brand-primary-default, #06f);
  display: inline-block;
  font-weight: 600;
  line-height: 150%;
  margin: 2px 3px;
  padding: 2px 6px;
  word-break: break-word;
  outline: none;
  cursor: text;
}

// 引用对话部分Css
.chatInputReference {
  margin: 12px;
  display: flex;
  align-items: center;
  min-height: 36px;
  background: #f6f8fd;
  border-radius: 4px;
  padding: 0 12px 0 14px;
  overflow-y: auto;

  img {
    margin-left: 4px;
    flex-shrink: 0;
  }

  p {
    margin-left: 4px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: normal;
    line-height: 1.5;
    letter-spacing: normal;
    color: #555;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: calc(100% - 24px);
  }
}

.customChatContentWrapper {
  :global(.semi-markdownRender:has(> :nth-child(1))) {
    width: 756px;
  }
}

.customChatContent {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 760px;
  word-wrap: break-word;

  :global(.semi-button) {
    border-radius: 10px;
    padding: 7px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #3D3D3D;

    :global(.semi-button-content) {
      font-size: 14px;
      gap: 6px !important;

      :global(.semi-button-content-left) {
        margin-right: 0;
      }
    }
  }

  :global(.semi-input-textarea) {
    background: #fff;
    padding: 12px !important;
    border: 1px solid #dcdcdc;
    border-radius: 8px;
  }

  // 覆盖markdownRender的样式
  :global(.semi-markdownRender) {
    @base-size: 24;
    @decrement: 2;


    * {
      white-space: normal;
      overflow-wrap: break-word;
      word-wrap: break-word;
      word-break: break-word;
      line-height: 24px;
    }

    br, hr {
      margin: 10px 0;
    }

    br,
    hr {
      margin: 10px 0;
    }

    .generate-headings(@i) when (@i <=7) {
      h@{i} {
        font-size: (@base-size - (@i - 1) * @decrement) * 1px !important;
      }

      .generate-headings(@i + 1);
    }

    .generate-headings(1);

    ul,
    li {
      font-size: 14px !important;
    }
  }
}

.chatInputAction {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  margin-top: 12px;
}

.editContent {
  display: flex;
  align-items: center;
  justify-content: center;

  :global(.semi-input-textarea-wrapper) {
    border-radius: 8px;
    width: 350px!important;
  }
}

.defaultContent {
  max-width: 550px;
  border-radius: 8px;
  background: #005bf8;
  padding: 8px 12px;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;

  :global(.semi-typography) {
    color: #fff;
  }

  :global(.semi-markdown-render-component-p) {
    font-size: 14px;
    line-height: 22px;
    color: #fff;
  }
}

.bhWhite {
  background: transparent;
}

.userImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;

  img {
    margin: 0 auto;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 引用项样式
.referenceItem {
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  font-size: 12px;
  height: 32px;
  width: 100%;
  word-break: break-word;
  flex-shrink: 0;

  .referenceItemContent {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .referenceItemImage {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 4px;
    object-fit: cover;
  }

  .referenceItemFileIcon {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;

    &:global(.fileIcon-smart) {
      width: 32px !important;
      height: 32px !important;
      transform: scale(0.5);
      margin: 0 -6px 0 -10px;
    }

  }

  .referenceDelete {
    display: inline-flex;
    align-items: center;
    cursor: pointer;

    img {
      width: 10px;
      height: 10px;
      margin: 0;
    }
  }
}

// 消息中的引用按钮
.messageReferenceAction {
  width: 24px;
  height: 24px;
  position: absolute;
  right: 4px;
  bottom: 15px;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 2;

  button {
    padding: 4px !important;
    height: 24px;
    width: 24px;
    background-color: rgba(255, 255, 255, 90%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #fff;
    }

    svg {
      width: 16px;
      height: 16px;
      color: #666;
    }
  }
}


.customChatContent:hover .messageReferenceAction {
  opacity: 1;
}

// 默认提示样式
.defaultPromptContainer {
  display: flex;
  padding: 0 12px;
}

.defaultPrompt {
  cursor: pointer;
  background-color: #f6f8fd;
  color: #000;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  display: inline-block;
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:hover {
    background-color: #e6f0ff;
    border-color: #005bf8;
  }
}

.chatAction {
  margin: 4px 0 0;

  :global(.semi-button) {
    height: 16px;
    padding: 0 !important;
    column-gap: 12px;
  }
}

.enlarge {
  text-align: right;
  padding: 10px 17px 0 0;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.enlarge img {
  cursor: pointer;
}

.answerContent {
  border-radius: 12px;
  display: flex;
  align-items: center;
  flex-direction: row;
  max-width: 100%;
  min-width: 0;
  padding: 10px 14px;
  width: fit-content;
  border: 1px solid rgba(0,0,0,30%);
  cursor: pointer;

  .answerContentIcon, .vditorTitle {
    display: block;
  }
}

.answerContentIcon {
  display: none;
  width: 16px;
  height: 16px;
  color: rgba(0, 0, 0, 55%);
  margin-right: 4px;
}

.vditorTitle {
  display: none;
}

.loadingContainer {
	display: inline-flex;
	align-items: center;
	padding: 8px 0;
	gap: 8px;

  .text {
    color: #1677ff;
    font-size: 14px;
  }
}

.rsLoading{
  padding: 8px 16px;
  border-radius: 8px;
  // background: #F6F8FD;
  display: flex;
  align-items: center;
  line-height: 0;
}

.rsLoadingText{
  font-size: 14px;
  margin-left: 4px;
  line-height: 22px;
  color: rgb(61, 61, 61);
}

:global(.semi-spin-wrapper) {
  color: #999;
}
