import EmptyImage from '@/assets/chat/fallbackImage.svg';
import deleteImage from '@/assets/svg/deletesm.svg';
import LazyImage from '@/components/LazyImage';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { VisualEngineeringListItemType } from '@/services/visualEngineering';
import { formatTimestamp } from '@/utils/util';
import { Popconfirm, Spin } from '@douyinfe/semi-ui';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ImagePreviewer from '../ImagePreviewer';
import styles from './index.less';

export interface GalleryProps {
  active: boolean;
  pageSize?: number;
  refreshKey: number;
  fetchImages: () => Promise<VisualEngineeringListItemType[]>;
  onDelete: (id: number) => Promise<boolean>;
}

const RecordList = forwardRef<{ loadMoreImages: () => void }, GalleryProps>(
  ({ active, refreshKey, pageSize = 10, fetchImages, onDelete }, ref) => {
    const previewRefs = useRef<Record<number, { open: () => void }>>({});
    const [allImages, setAllImages] = useState<VisualEngineeringListItemType[]>([]);
    const allImagesRef = useRef(allImages);
    const [images, setImages] = useState<VisualEngineeringListItemType[] | null>(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
      const initData = async () => {
        try {
          setLoading(true);
          const data = await fetchImages();
          setAllImages(data);
          setImages(data.slice(0, pageSize));
          setHasMore(data.length > pageSize);
          setError(null);
        } catch (error) {
          setError('加载图片失败');
          console.error('Failed to fetch images:', error);
        } finally {
          setLoading(false);
        }
      };
      if (active) {
        initData();
        setPage(1);
      }
    }, [active, refreshKey]);

    const loadMoreImages = useCallback(async () => {
      if (!hasMore || loading) return;

      setLoading(true);
      try {
        const startIndex = page * pageSize;
        const endIndex = startIndex + pageSize;
        const nextImages = allImagesRef.current.slice(startIndex, endIndex);

        await new Promise((resolve) => setTimeout(resolve, 200));

        setImages((prev) => [...(prev || []), ...nextImages]);
        setPage((prev) => prev + 1);
        setHasMore(endIndex < allImagesRef.current.length);
        setError(null);
      } catch (err) {
        setError('加载更多失败');
        console.error('Failed to load more images:', err);
      } finally {
        setLoading(false);
      }
    }, [hasMore, loading, page, pageSize]);

    // 使用 Intersection Observer 替代滚动事件
    const { triggerRef } = useInfiniteScroll(loadMoreImages, hasMore, loading, {
      rootMargin: '100px 0px',
      threshold: 0.1,
      debounceMs: 150,
    });

    useEffect(() => {
      allImagesRef.current = allImages;
    }, [allImages]);

    const handleImageClick = useCallback((id: number) => {
      previewRefs.current[id]?.open();
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        loadMoreImages,
      }),
      [loadMoreImages],
    );

    // 优化删除操作
    const handleDelete = useCallback(
      async (id: number) => {
        try {
          if (await onDelete(id)) {
            setAllImages((prev) => prev.filter((img) => img.id !== id));
            setImages((prev) => (prev ? prev.filter((img) => img.id !== id) : null));
            return true;
          }
          return false;
        } catch (error) {
          console.error('删除失败:', error);
          return false;
        }
      },
      [onDelete],
    );

    return (
      <div className={styles.recordList}>
        <div className={styles.recordListName}>最近记录</div>
        {images == null ? null : images.length ? (
          <div className={styles.recordListContent}>
            {images.map((item) => (
              <div
                className={styles.galleryItem}
                key={item.id}
                onClick={() => handleImageClick(item.id)}
              >
                <LazyImage url={item.identUrl} alt={''} height={221} />
                <div className={styles.recordListContentItemBot}>
                  <div className={styles.recordListContentItemBotTL}>
                    <div className={styles.recordListContentItemBotTLT}>
                      统计结果：{item.identCount}
                    </div>
                    <div className={styles.recordListContentItemBotTLB}>
                      {formatTimestamp(item.createTime)}
                    </div>
                  </div>
                  <Popconfirm
                    title="确定要删除此条记录吗？"
                    content="删除后，内容无法恢复。"
                    onConfirm={() => handleDelete(item.id)}
                  >
                    <div
                      className={styles.recordListContentItemBotTR}
                      onClick={async (e) => {
                        e.stopPropagation();
                      }}
                    >
                      <img
                        src={deleteImage}
                        alt=""
                        className={styles.recordListContentItemDelete}
                      />
                    </div>
                  </Popconfirm>
                </div>
                <ImagePreviewer
                  ref={(el) => {
                    if (el) previewRefs.current[item.id] = el;
                    else delete previewRefs.current[item.id];
                  }}
                  mainUrl={item.identUrl}
                  allUrls={[item.identUrl]}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyMessageContainer}>
            <img className={styles.emptyImage} src={EmptyImage} />
            <div className={styles.emptyMessageText}>暂无历史记录</div>
          </div>
        )}
        {/* Intersection Observer 触发器 */}
        {hasMore && images && images.length > 0 && (
          <div ref={triggerRef} className={styles.loadTrigger} />
        )}

        {loading && (
          <div className={styles.recordListSpin}>
            <Spin size="large" />
            <span>加载中...</span>
          </div>
        )}

        {error && (
          <div className={styles.errorMessage}>
            <p>{error}</p>
          </div>
        )}

        {!hasMore && images && images.length > 0 && (
          <div className={styles.endMessage}>
            <p>已经到底啦~</p>
          </div>
        )}
      </div>
    );
  },
);

export default RecordList;
