import styles from '@/styles/officetemplate-common.less';
import { Row, Spin } from '@douyinfe/semi-ui';
import React from 'react';
import ToolCard from './ToolCard';

interface ToolListProps {
  loading: boolean;
  tools: any[];
  selectedCardIndex: number | null;
  onCardClick: (index: number) => void;
}

const ToolList: React.FC<ToolListProps> = ({ loading, tools, selectedCardIndex, onCardClick }) => {
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  if (tools.length === 0) {
    return <div className={styles.emptyContainer}>暂无可用工具，请稍后再试</div>;
  }

  return (
    <Row gutter={[24, 24]} style={{ margin: 0 }}>
      {tools.map((tool, index) => (
        <ToolCard
          key={index}
          tool={tool}
          index={index}
          selectedIndex={selectedCardIndex}
          onClick={onCardClick}
        />
      ))}
    </Row>
  );
};

export default ToolList;
