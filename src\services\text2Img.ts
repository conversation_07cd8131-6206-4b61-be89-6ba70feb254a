import request from '@/utils/request';

export const textImageMenuListApi = '/ai/image/getTextImageMenuList';
export const textImageHistoryApi = '/ai/image/getImageHistory';
export const textImageHistoryDeleteApi = '/ai/image/delImageHistory';
export const textImageCallbackApi = '/ai/image/callBackImageStatus';

// 文生图比例类型定义
export interface RatiosType {
  code: string;
  name: string;
}

// 风格类型定义
export interface StylesType {
  code: string;
  name: string;
}

export interface ImagesType {
  type: string;
  name: string;
  url: string;
  size: string;
  wordCount: number;
  width: number;
  height: number;
  transferUrl: string;
  fileTags: ImagesType[];
}

export interface TextImageMenuList {
  batchSizes: number[]; // 图片个数
  prompt: string; // 提示词
  routeId: number; // 二级功能id
  routeValue: string; // 二级功能名称
  type: number; // 类型
  ratios: RatiosType[]; // 比例类型
  styles: StylesType[]; // 风格类型
}

export interface TextImageHistory {
  id: number;
  routeId: number;
  chatId: string;
  chatResId: string;
  coverImage: ImagesType;
  images: ImagesType[];
}

/**
 * 获取文生图菜单列表
 * @param { routerId: number } 二级功能id
 * @returns
 */
export const fetchTextImageMenuList = (params?: { routerId?: number }) =>
  request<TextImageMenuList[]>(textImageMenuListApi, {
    method: 'GET',
    params,
  });

/**
 * 获取文生图历史记录
 * @param { routeId: number } 二级功能id
 * @returns
 */
export const fetchTextImageHistory = (params?: { routeId?: number | string }) =>
  request<TextImageHistory[]>(textImageHistoryApi, {
    method: 'GET',
    params,
  });

/**
 * 删除文生图历史记录
 * @param { id: number } 历史记录id
 * @returns
 */
export const deleteTextImageHistory = (params: { id: number }) =>
  request<any>(textImageHistoryDeleteApi, {
    method: 'GET',
    params,
  });

/**
 * 文生图回调
 * @param {messageId: string, status: number, images: ImagesType[]} 文生图id, 文生图状态, 文生图图片
 * @returns
 */
export const textImageCallback = (params: {
  messageId: string;
  status: number;
  images: ImagesType[];
}) =>
  request<any>(textImageCallbackApi, {
    method: 'POST',
    data: params,
  });
