// @import '~vditor/src/assets/less/index.less';
ol {
  list-style: decimal;
}

.vditor-wysiwyg pre.vditor-reset {
  padding-bottom:80px;
}

.vditor-reset table {
	width: 85%!important;
  max-width: 800px;
}

.vditor-wysiwyg a{
  display: inline;
  color: var(--semi-color-link);
  cursor: pointer;
  text-decoration: none;
}
// 自定义样式覆盖
.vditor {
  &-toolbar {
    border-bottom: 1px solid #eaecef;
  }

  &-editor {
    background-color: #fff;
  }

  &-preview {
    background-color: #fff;
  }
}

// 编辑器容器样式
.editorWrap {
  width: 100%;
  background: #fff;
  border: 1px solid #eaecef;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease-in-out;
  transform-origin: center;

  &.closing {
    transform: scale(0);
    opacity: 0;
  }

  #vditor {
    height: calc(100% - 40px); // 减去头部高度
  }
}

// 头部样式
.editorHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eaecef;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  .editorTitle {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .editorActions {
    display: flex;
    align-items: center;

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      margin-left: 8px;
      background-color: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      color: #666;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 5%);
        color: #1890ff;
      }
    }

    .copyBtn:hover {
      color: #52c41a;
    }

    .closeBtn:hover {
      color: #f5222d;
    }
  }
}


.loadingContainer {
	position: absolute;
    bottom: 7%;
    right: 50px;
	display: inline-flex;
	align-items: center;
	padding: 8px 16px;
	background-color: #f5f7fa;
	border-radius: 20px;
	gap: 8px;
	border: 1px solid #1677ff;
  }

  .icon {
	width: 16px;
	height: 16px;
	fill: #1677ff;
	// animation: rotate 1s linear infinite;
  }

  .text {
	color: #1677ff;
	font-size: 14px;
  }

  @keyframes rotate {
	from {
	  transform: rotate(0deg);
	}

	to {
	  transform: rotate(360deg);
	}
  }
