// 不显示联网查询、appCodes
export const NO_ONLINE_APPS = [
  'knowledge',
  'engin',
  'report_compare',
  'report_valid',
  'ppt',
  'image',
];
// 不显示上传文件、appCode
export const NO_UPLOAD_FILE_APPS = [
  'knowledge',
  'engin',
  'report_compare',
  'report_valid',
  'image',
];
// 不显示上传图片、appCode
export const NO_UPLOAD_IMAGE_APPS = [
  'knowledge',
  'engin',
  'report_compare',
  'report_valid',
  'image',
];

// 文生图按钮控制
export const TEXT_CREATION_IMAGE_BUTTON = ['image'];

// 文生图具体按钮控制根据routerId
export const TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID = {
  image: ['4001', '4002', '4003', '4004', '4005', '4006'], // 参考图
  image_text: ['4004', '4005', '4006'], // 原图片文案
  image_ratio: ['4001', '4002', '4003'], // 比例
  image_style: ['4001', '4002', '4003'], // 风格
  image_count: ['4001', '4002', '4003'], // 个数
  image_color: ['4004'], // 背景颜色
};

// 文生图二级导航第4 5 6项，必须上传图片的routerId
export const IMAGE_SECOND_NAV_ITEMS_4_5_6 = ['4004', '4005', '4006'];

// 文生图图片上传数量
export const TEXT_CREATION_IMAGE_COUNT = 1;

// 顶部类型
export const MENUS_APPCODES = ['knowledge'];

// 上传不展示默认提示词
export const NO_DEFAULT_PROMPT_APPS = ['engin', 'report_valid', 'report_compare', 'ppt'];

// 计算书审核显示标准设计和计算书按钮
export const SHOW_STANDARD_AND_CALCULATE_BUTTON_APPS = ['engin', 'report_compare', 'report_valid'];

// 计算树对话不显示引用文件
export const NO_REFERENCE_FILE_APPS = ['engin', 'report_compare'];

// 上传文件最大为一个
export const UPLOAD_ONE_FILE_APPS = ['report_valid', 'ppt'];

// 不展示复制按钮
export const NO_COPY_BUTTON_APPS = ['report_valid'];

// 不展示模板
export const NO_TEMPLATE_APPS = ['ppt'];

// 不展示整个模板头
export const NO_TEMPLATE_HEADER_APPS = ['image'];

// 没有对话引用文件按钮功能的APP
export const NO_REFERENCE_FILE_BUTTON_APPS = ['image'];

// 需要重连消息的appCode
export const NEED_RECONNECT_APPS = ['image'];
