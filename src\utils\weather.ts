// 冰雹
import weaterBb from '@/assets/chat/weatherSvg/weaterBb.svg';
// 暴风雪
// import weaterBfx from "@/assets/chat/weatherSvg/weaterBfx.svg"
// 暴雪
import weaterBx from '@/assets/chat/weatherSvg/weaterBx.svg';
// 暴雨
import weaterBy from '@/assets/chat/weatherSvg/weaterBy.svg';
// 吹沙
import weaterCs from '@/assets/chat/weatherSvg/weaterCs.svg';
// 大暴雨
import weaterDby from '@/assets/chat/weatherSvg/weaterDby.svg';
// 大雪
import weaterDx from '@/assets/chat/weatherSvg/weaterDx.svg';
// 多云
import weaterDy from '@/assets/chat/weatherSvg/weaterDy.svg';
// 大雨
import weaterDyd from '@/assets/chat/weatherSvg/weaterDyd.svg';
// 风
import weaterF from '@/assets/chat/weatherSvg/weaterF.svg';
// 雷雨
import weaterLy from '@/assets/chat/weatherSvg/weaterLy.svg';
// 晴天
import weaterQt from '@/assets/chat/weatherSvg/weaterQt.svg';
// 晴转多云
// import weaterQzdy from '@/assets/chat/weatherSvg/weaterQzdy.svg';
// 沙尘暴
import weaterScb from '@/assets/chat/weatherSvg/weaterScb.svg';
// 闪电
// import weaterSd from '@/assets/chat/weatherSvg/weaterSd.svg';
// 台风
// import weaterTf from '@/assets/chat/weatherSvg/weaterTf.svg';
// 雾
import weaterW from '@/assets/chat/weatherSvg/weaterW.svg';
// 小雪
import weaterXx from '@/assets/chat/weatherSvg/weaterXx.svg';
// 小雨
import weaterXy from '@/assets/chat/weatherSvg/weaterXy.svg';
// 雨夹雪
import weaterYjx from '@/assets/chat/weatherSvg/weaterYjx.svg';
// 阴雾
import weaterYm from '@/assets/chat/weatherSvg/weaterYm.svg';
// 阴天
import weaterYt from '@/assets/chat/weatherSvg/weaterYt.svg';
// 中雪
import weaterZx from '@/assets/chat/weatherSvg/weaterZx.svg';
// 中雨
import weaterZy from '@/assets/chat/weatherSvg/weaterZy.svg';

// 创建天气现象与 SVG 文件的映射表
const weatherMap: { [key: string]: string } = {
  晴天: weaterQt,
  多云: weaterDy,
  阴: weaterYt,
  阵雨: weaterXy,
  雷阵雨: weaterLy,
  雷阵雨并伴有冰雹: weaterBb,
  雨夹雪: weaterYjx,
  小雨: weaterXy,
  中雨: weaterZy,
  大雨: weaterDyd,
  暴雨: weaterBy,
  大暴雨: weaterDby,
  特大暴雨阵雪: weaterDby,
  雪: weaterXx,
  阵雪: weaterXx,
  小雪: weaterXx,
  中雪: weaterZx,
  大雪: weaterDx,
  暴雪: weaterBx,
  雾: weaterW,
  冻雨: weaterYjx,
  沙尘暴: weaterScb,
  小到中雨: weaterXy,
  中到大雨: weaterZy,
  大到暴雨: weaterDyd,
  暴雨到大暴雨: weaterBy,
  大暴雨到特大暴雨: weaterDby,
  小到中雪: weaterXx,
  中到大雪: weaterZx,
  大到暴雪: weaterDx,
  浮尘: weaterCs,
  扬沙: weaterCs,
  强沙尘暴: weaterScb,
  浓雾: weaterW,
  强浓雾: weaterW,
  霾: weaterYm,
  中度霾: weaterYm,
  重度霾: weaterYm,
  严重霾: weaterYm,
  大雾: weaterW,
  特强浓雾: weaterW,
  无: weaterQt,
  雨: weaterXy,
  风: weaterF,
  无持续风向: weaterF,
  东北风: weaterF,
  东风: weaterF,
  东南风: weaterF,
  南风: weaterF,
  西南风: weaterF,
  西风: weaterF,
  西北风: weaterF,
  北风: weaterF,
  旋转风: weaterF
};

export default (weather = '') => {
  return weatherMap[weather] || weaterQt;
};
