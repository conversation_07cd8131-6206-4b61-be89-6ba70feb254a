import React, {
  forwardRef,
  Fragment,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createEditor, Descendant, Node, Element as SlateElement } from 'slate';
import { withHistory } from 'slate-history';
import {
  Editable,
  RenderElementProps,
  RenderLeafProps,
  RenderPlaceholderProps,
  Slate,
  withReact,
} from 'slate-react';

import { CustomEditor, MentionElement, RenderElementPropsFor } from './custom-types';

import { CHAT_PLACEHOLDER } from '@/config/chat';
import { getState } from '@/utils';
import { safeResetSlateEditor, validateSlateNodes } from '@/utils/slateHelpers';
import classNames from 'classnames';
import { useParams } from 'umi';
import Styles from '../index.less';

const IS_MAC = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent);

// 扩展功能
const withMentions = (editor: CustomEditor) => {
  const { isInline, isVoid } = editor;

  editor.isVoid = (element: SlateElement) => isVoid(element);

  editor.isInline = (element: SlateElement) => {
    return element.type === 'labeled_input' ? true : isInline(element);
  };

  return editor;
};

// Borrow Leaf renderer from the Rich Text example.
// In a real project you would get this via `withRichText(editor)` or similar.
const Leaf = ({ attributes, children, leaf }: RenderLeafProps) => {
  let newChil = children;
  if (leaf.bold) {
    newChil = <strong>{children}</strong>;
  }

  if (leaf.code) {
    newChil = <code>{children}</code>;
  }

  if (leaf.italic) {
    newChil = <em>{children}</em>;
  }

  if (leaf.underline) {
    newChil = <u>{children}</u>;
  }

  return <span {...attributes}>{newChil}</span>;
};

/**
 * 自定义渲染组件
 * @param param
 * @returns
 */
const Mention = ({ attributes, children, element }: RenderElementPropsFor<MentionElement>) => {
  const style: React.CSSProperties = {
    padding: '3px 3px 2px',
    margin: '0 4px',
    verticalAlign: 'baseline',
    borderRadius: '4px',
    backgroundColor: '#EBF2FF',
    color: '#005BF8',
    fontSize: '16px',
  };

  const myRef = useRef<HTMLDivElement>(null);

  const [mWidth, setMWidth] = useState(0);

  useEffect(() => {
    const element = myRef.current;
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const width = entry.contentRect.width + 8;
          setMWidth(width);
        }
      });
      resizeObserver.observe(element);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [element.placeholder]);

  return (
    <span
      {...attributes}
      suppressContentEditableWarning
      contentEditable={true}
      className={Styles.inputContainer}
      style={!element.children[0].text ? { minWidth: mWidth + 'px', ...style } : style}
    >
      {/* placeholder */}
      <div className={Styles.startPoint} suppressContentEditableWarning contentEditable="false">
        {element.placeholder && (
          <div
            ref={myRef}
            className={classNames([Styles.placeholder, { invisible: element.children[0].text }])}
          >
            {element.placeholder}
          </div>
        )}
      </div>
      <span
        contentEditable="false"
        suppressContentEditableWarning
        style={{ verticalAlign: 'top', fontSize: '0px', userSelect: 'none' }}
      >
        &nbsp;
      </span>
      {/* Prevent Chromium from interrupting IME when moving the cursor */}
      {/* 1. span + inline-block 2. div + contenteditable=false */}
      {IS_MAC ? (
        // Mac OS IME https://github.com/ianstormtaylor/slate/issues/3490
        <Fragment>
          {children}
          {element.character}
        </Fragment>
      ) : (
        // Others like Android https://github.com/ianstormtaylor/slate/pull/5360
        <Fragment>
          {element.character}
          {children}
        </Fragment>
      )}
      <span
        contentEditable="false"
        suppressContentEditableWarning
        style={{ verticalAlign: 'top', fontSize: '0px', userSelect: 'none' }}
      >
        &nbsp;
      </span>
    </span>
  );
};

/**
 * 渲染节点
 * @param param
 * @returns
 */
const Element = (props: RenderElementProps) => {
  const { attributes, children, element } = props;
  switch (element.type) {
    case 'labeled_input':
      return <Mention {...props} />;
    default:
      return <div {...attributes}>{children}</div>;
  }
};

/**
 * 获取文本内容
 * @param param
 * @returns
 */
const serialize = (value: any) => {
  return (
    value
      // Return the string content of each paragraph in the value's children.
      .map((n: any) => Node.string(n))
      // Join them all with line breaks denoting paragraphs.
      .join('\n')
  );
};

interface RichInputProps {
  onChange?: (value: string) => void;
  onSend?: () => void;
  beforeSend?: () => boolean;
  /**
   * 初始化内容
   */
  initialValue: Descendant[];
  placeholder?: string;
  type?: Record<string, any>;
}

// 定义暴露方法的类型
export interface RichInputFn {
  /**
   * 重置默认值
   * @returns
   */
  resetDefaultInput: () => void;
}

/**
 * 富文本编辑器
 * @returns
 */
const RenderRichInput = forwardRef<RichInputFn, RichInputProps>((props, ref) => {
  // 初始化内容
  const initialValue = [
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ] as Descendant[];
  const { chatId = '' } = useParams<{ chatId: string }>();

  // 优化性能
  const renderElement = (props: RenderElementProps) => <Element {...props} />;
  const renderLeaf = (props: RenderLeafProps) => <Leaf {...props} />;
  const editor = useMemo(
    () => withMentions(withReact(withHistory(createEditor()))) as CustomEditor,
    [],
  );

  const resetDefaultInput = () => {
    safeResetSlateEditor(editor);
    props.onChange?.('');
  };

  // 使用 useImperativeHandle 暴露方法
  useImperativeHandle(ref, () => ({
    resetDefaultInput,
  }));

  // 初始化值
  useEffect(() => {
    if (props.initialValue && Array.isArray(props.initialValue) && props.initialValue.length > 0) {
      if (editor && editor.children) {
        try {
          // 使用辅助函数验证节点结构
          const validatedValue = validateSlateNodes(props.initialValue);

          // 重新赋值
          editor.children = validatedValue;
          setTimeout(() => {
            props?.onChange?.(serialize(editor.children));
          }, 200);
        } catch (error) {
          console.error('Error initializing rich input:', error);
          // 如果验证失败，使用安全的默认值
          safeResetSlateEditor(editor);
          props?.onChange?.('');
        }
      }
    }
  }, [props.initialValue, editor]);

  // 监听键盘事件
  const onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      } else {
        // 普通回车，阻止默认换行行为并发送内容
        event.preventDefault();
        switch (event.key) {
          case 'Enter': {
            if (chatId) {
              if (getState().chat.chatMsg![chatId]?.pending) return;
            }
            if (props?.beforeSend && !props?.beforeSend?.()) {
              return;
            }
            props?.onSend?.();
            resetDefaultInput();
            event.preventDefault();
            break;
          }
          default:
            break;
        }
      }
    }
  };

  return (
    <Slate
      key={JSON.stringify(props.initialValue)}
      editor={editor}
      initialValue={initialValue}
      onChange={() => {
        try {
          // 安全地获取编辑器内容
          if (editor && editor.children && editor.children.length > 0) {
            const text = serialize(editor.children);
            props.onChange?.(text);
          }
        } catch (error) {
          console.error('Error in Slate onChange:', error);
          // 如果出错，尝试重置编辑器状态
          try {
            editor.children = [{ type: 'paragraph', children: [{ text: '' }] }] as Descendant[];
            editor.selection = null;
            props.onChange?.('');
          } catch (resetError) {
            console.error('Error resetting editor in onChange:', resetError);
          }
        }
      }}
    >
      <Editable
        renderElement={renderElement}
        renderLeaf={renderLeaf}
        onKeyDown={onKeyDown}
        placeholder={props.placeholder || CHAT_PLACEHOLDER}
        renderPlaceholder={({ children, attributes }: RenderPlaceholderProps) => (
          <span
            {...attributes}
            style={{
              ...attributes.style,
              opacity: 1,
              fontSize: '16px',
              color: '#ccc',
              fontFamily: 'PingFang SC',
            }}
          >
            {children}
          </span>
        )}
      />
    </Slate>
  );
});

export default RenderRichInput;
