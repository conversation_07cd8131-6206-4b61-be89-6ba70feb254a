.recordList {
  width: 800px;
  padding: 4px;
  .recordListName {
    font-family: PingFang SC;
    font-size: 12px;
    color: #3d3d3d;
    margin: 16px 0 12px;
  }
  .recordListContent {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    .galleryItem {
      width: 192px;
      height: 279px;
      border-radius: 4px;
      transition: all 0.3s ease;
      .recordListContentItemImg {
        width: 100%;
        height: 221px;
        cursor: pointer;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background-size: cover;
        // object-fit: cover;
        // display: block;
      }
      .recordListContentItemBot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 58px;
        padding: 0 8px;
        background: #f8faff;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        .recordListContentItemBotTL {
          .recordListContentItemBotTLT {
            font-family: Source Han Sans CN;
            font-size: 14px;
            font-weight: 500;
            color: #000;
          }
          .recordListContentItemBotTLB {
            font-family: PingFang SC;
            font-size: 12px;
            color: #999;
          }
        }
        .recordListContentItemBotTR {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #ebedf4;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          visibility: hidden;
          .recordListContentItemDelete {
            width: 16px;
            height: 16px;
          }
        }
      }
      &:hover {
        transform: translateY(-2px) scale(1.01);
      }
      &:hover .recordListContentItemBotTR {
        visibility: visible;
      }
    }
  }
  .emptyMessageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .emptyImage {
      width: 160px;
      height: 160px;
    }
    .emptyMessageText {
      font-family: PingFang SC;
      font-size: 14px;
      color: #3d3d3d;
      margin-top: 12px;
    }
  }
  .recordListSpin {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    :global(.semi-spin-wrapper) {
      color: #3498db !important;
    }
    span {
      margin-left: 12px;
    }
  }
  /* 结束消息 */
  .endMessage {
    padding: 20px;
    color: #999;
    font-size: 15px;
    text-align: center;
  }

  .endMessage p {
    margin-top: 30px;
  }
}

.loadTrigger {
  height: 1px;
  width: 100%;
  visibility: hidden;
}

.errorMessage {
  text-align: center;
  padding: 20px;
  color: #ff4d4f;
  font-size: 14px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  margin: 10px 0;
}

.endMessage {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}