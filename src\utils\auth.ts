// token key
export const ACCESS_TOKEN_KEY = 'ACCESS_TOKEN__';

export const REFRESH_TOKEN_KEY = 'REFRESH_TOKEN__';

const env = process.env.UMI_ENV || 'dev';

export const TENANT_ID_KEY = `TENANT_ID__${env.toUpperCase()}`;

/**
 * 生成拼接key
 * @param key
 * @returns 拼接的key
 */
export function generateLocalCommon(key: string): string {
  return `COMMON_KEY_${key}`;
}

/**
 * 清除auth
 */
export function clearCommonAuthCache() {
  localStorage.setItem(generateLocalCommon(ACCESS_TOKEN_KEY), '');
  localStorage.setItem(generateLocalCommon(REFRESH_TOKEN_KEY), '');
}
/**
 * 获取auth通用方法
 * @param key
 * @returns string
 */
export function getCommonAuthCache(key: string): string {
  let value = localStorage.getItem(generateLocalCommon(key)) as string;
  return value === 'undefined' ? '' : value;
}

type commonCacheType = string | undefined;
/**
 * 设置auth通用方法
 * @param key
 * @param value
 * @returns
 */
export function setCommonAuthCache(key: string, value: commonCacheType): commonCacheType {
  return localStorage.setItem(
    generateLocalCommon(key),
    typeof value === 'undefined' ? '' : (value as string),
  ) as commonCacheType;
}

export function getAccessToken(): string {
  return getCommonAuthCache(ACCESS_TOKEN_KEY) as any;
}
export function setAccessToken(value?: string) {
  return setCommonAuthCache(ACCESS_TOKEN_KEY, value);
}

export function getRefreshToken(): string {
  return getCommonAuthCache(REFRESH_TOKEN_KEY) as any;
}

export function setRefreshToken(value: string) {
  return setCommonAuthCache(REFRESH_TOKEN_KEY, value);
}

export function getTenantId() {
  return getCommonAuthCache(TENANT_ID_KEY) as any;
}

export function setTenantId(value: string) {
  return setCommonAuthCache(TENANT_ID_KEY, value);
}
