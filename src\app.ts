import { getTenantIdByCode } from './services/login';
import { dispatchInUtils, getState, getTenantId, setTenantId } from './utils';

// 全局初始化逻辑
export const render = async (oldRender: () => void) => {
  try {
    const tenantId = getTenantId();
    if (!tenantId || tenantId === 'null') {
      const res = await getTenantIdByCode('cloud_ai');
      setTenantId(res.data.toString());
    }
    oldRender();
  } catch (error) {
    oldRender(); // 即使失败也要渲染，否则页面白屏
  }
};

export function onRouteChange({ location }: any) {
  const { pathname } = location;
  if (pathname !== '/login' && pathname !== '/4Alogin') {
    // 获取用户信息
    if (!getState().auth?.user?.id) {
      dispatchInUtils({ type: 'auth/fetchUser' });
    }
  }
}
