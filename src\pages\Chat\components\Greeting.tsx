import defaultNewChat from '@/assets/chat/defaultNewChat.png';
import { AuthModelState } from '@/models/auth';
import { getCity, getHomeSwiperList, getWeather } from '@/services/chat';
import TodayWeater from '@/utils/weather';
import { Carousel, Toast } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'umi';
import styles from './index.less';

/**
 * 根据时间合适问候语
 * @param hour
 * @param includeGood
 * @returns
 */
const getGreeting = (hour: number, includeGood: boolean = true): string => {
  let greetingPart = '';
  if (hour >= 5 && hour < 12) greetingPart = '早上好';
  else if (hour >= 12 && hour < 18) greetingPart = '下午好';
  else if (hour >= 18 && hour < 22) greetingPart = '晚上好';
  else greetingPart = '夜深了';

  return includeGood ? `${greetingPart}` : greetingPart;
};

/**
 * 格式化时间戳为月日和时分
 * @param timestamp number
 * @param type 'date' | 'time'
 * @returns string time
 */
const formatTimestamp = (timestamp: number, type: 'date' | 'time'): string => {
  const date = new Date(timestamp);
  if (type === 'date') {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  } else {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
};

/**
 * 早中晚问候
 * @param props { title?: string } - 可选的标题属性
 * @description 根据当前时间显示不同的问候语
 * @returns jsx
 */
export const GreetingRender = (props: { title?: string }) => {
  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);

  const hours = new Date().getHours();
  const greeting = getGreeting(hours);

  return (
    <div
      className={classNames([styles.chatTopSlot, styles['greeting-animation'], styles.greeting])}
    >
      {props.title || `${greeting}，${user?.name || ''}`}
    </div>
  );
};

/**
 * 天气横幅轮播
 * @returns jsx
 */
export const WeatherBannerRender = () => {
  const [data, setData] = useState<{
    swiperList: {
      createTime: number;
      creator: null | string;
      deleted: boolean;
      id: number;
      title: string;
      updateTime: number;
      updater: null | string;
      url: string;
    }[];
    city: string;
    weather: { dayweather: string; daytemp: string; nighttemp: string; temperature: string } | null;
    currentTime: string;
    currentDate: string;
  }>({
    swiperList: [],
    city: '',
    weather: null,
    currentTime: '',
    currentDate: '',
  });

  const hours = new Date().getHours();
  const greeting = getGreeting(hours, false);
  const carouselRef = useRef<Carousel | null>(null);

  const setDefaultImageListAndTime = (prev: any, imageList: any[], nowTime: number) => {
    const defaultImageList =
      imageList.length === 0
        ? [
            {
              createTime: 0,
              creator: null,
              deleted: false,
              id: 0,
              title: '',
              updateTime: 0,
              updater: null,
              url: defaultNewChat,
            },
          ]
        : imageList;

    return {
      ...prev,
      swiperList: defaultImageList,
      currentTime: formatTimestamp(nowTime, 'time'),
      currentDate: formatTimestamp(nowTime, 'date'),
    };
  };

  const fetchData = async () => {
    try {
      const [swiperRes, cityRes] = await Promise.all([getHomeSwiperList(), getCity()]);

      if (cityRes.adcode) {
        try {
          // 获取当前天气数据
          const weatherRes = await getWeather(cityRes.adcode, 'now');
          const currentWeather = weatherRes?.realtime?.[0]?.infos || {
            weather: '未知',
            temperature: 'N/A',
          };
          // 获取未来天气数据
          const weatherFuture = await getWeather(cityRes.adcode, 'future');
          const futureWeather = weatherFuture?.forecast?.[0]?.infos?.[0] || {
            day: { temperature: 'N/A' },
            night: { temperature: 'N/A' },
          };
          // 更新天气数据
          setData((prev) => ({
            ...prev,
            weather: {
              ...prev.weather,
              daytemp: futureWeather.day.temperature,
              nighttemp: futureWeather.night.temperature,
              dayweather: currentWeather.weather,
              temperature: currentWeather.temperature,
            },
            city: cityRes.district || cityRes.city || '未知城市',
          }));
        } catch (error) {
          Toast.error('无法获取天气信息');
          setData((prev) => ({
            ...prev,
            weather: {
              daytemp: 'N/A',
              nighttemp: 'N/A',
              dayweather: '未知',
              temperature: 'N/A',
            },
            city: '未知城市',
          }));
        }
      } else {
        Toast.error(cityRes.message || '无法获取城市信息');
        setData((prev) => ({
          ...prev,
          weather: {
            daytemp: 'N/A',
            nighttemp: 'N/A',
            dayweather: '未知',
            temperature: 'N/A',
          },
          city: '未知城市',
        }));
      }

      if (swiperRes.data && swiperRes.data?.imageList) {
        const imageList = Array.isArray(swiperRes.data.imageList) ? swiperRes.data.imageList : [];
        setData((prev) => setDefaultImageListAndTime(prev, imageList, swiperRes.data.nowTime));
      } else {
        setData((prev) => setDefaultImageListAndTime(prev, [], new Date().getTime()));
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    fetchData();
    const intervalId = setInterval(() => {
      setData((prev) => ({
        ...prev,
        currentTime: formatTimestamp(new Date().getTime(), 'time'),
        currentDate: formatTimestamp(new Date().getTime(), 'date'),
      }));
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    if (data.swiperList.length > 1 && carouselRef.current) {
      carouselRef.current.play();
    }
  }, [data.swiperList]);

  return (
    <div
      className={styles.swiperData}
      onMouseEnter={() => carouselRef.current && carouselRef.current.stop()}
      onMouseLeave={() => carouselRef.current && carouselRef.current.play()}
    >
      {data.weather?.dayweather ? (
        <div className={styles.swiperItemContentL}>
          <div className={styles.swiperItemContentLTime}>{data.currentTime}</div>
          <div className={styles.swiperItemContentLDate}>
            {data.currentDate} {greeting}
          </div>
        </div>
      ) : null}
      {data.weather?.dayweather ? (
        <div className={styles.swiperItemContentR}>
          <div className={styles.swiperItemContentRWeather}>
            <div className={styles.swiperItemContentRWeatherTemp}>{data.weather?.temperature}℃</div>

            <img
              className={styles.swiperItemContentRWeatherIcon}
              src={TodayWeater(data.weather?.dayweather)}
            />
          </div>
          <div className={styles.swiperItemContentRWeatherCity}>
            {data.city} {data.weather?.nighttemp}°/{data.weather?.daytemp}°&nbsp;&nbsp;
            {data.weather?.dayweather}
          </div>
        </div>
      ) : null}
      {data.swiperList.length > 1 ? (
        <Carousel
          ref={carouselRef}
          className={styles.swiperCarosel}
          autoPlay={{ interval: 120000, hoverToPause: true }}
          showArrow={false}
          showIndicator={false}
        >
          {data.swiperList.map((item, index) => (
            <div
              key={index}
              className={styles.swiperItem}
              style={{ backgroundSize: '100% 100%', backgroundImage: `url('${item.url}')` }}
            >
              {item.title ? <div className={styles.swiperItemBotText}>{item.title}</div> : null}
            </div>
          ))}
        </Carousel>
      ) : (
        <>
          {data.swiperList.map((item, index) => (
            <div
              key={index}
              className={styles.swiperCarosel}
              style={{ backgroundSize: '100% 100%', backgroundImage: `url('${item.url}')` }}
            >
              {item.title ? <div className={styles.swiperItemBotText}>{item.title}</div> : null}
            </div>
          ))}
        </>
      )}
    </div>
  );
};
