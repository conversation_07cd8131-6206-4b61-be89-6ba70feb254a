import { FetchHistoryPPTParams } from '@/models/historyChat';
import request from '@/utils/request';

// 获取办公工具列表
export const getOfficeTools = (params: any) => {
  return request('/ai/write/queryWriteList', {
    method: 'POST',
    data: params,
  });
};

// 获取历史 PPT 数据
export const getHistoryPPTs = (data: FetchHistoryPPTParams) => {
  return request(`/ai/ppt/getAiPptPage?pageNo=${data.pageNum}&pageSize=${data.pageSize}`, {
    method: 'GET',
  });
};
//删除PPt模板
export const deletePPT = (pptId: number) => {
  return request(`/ai/ppt/deleteAiPptById?id=${pptId}`, {
    method: 'DELETE',
  });
};
