/**
 * 首页
 */
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, WeatherBannerRender } from '@/pages/Chat/components/Greeting';
import { fetchNewChatId } from '@/services/chat';
import { getState } from '@/utils';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import React from 'react';
import { useDispatch, useNavigate } from 'umi';
import AIChat from './Chat';
import styles from './index.less';
import type { QuestionsData } from './types';

const ChatPage: React.FC = () => {
  const navigate = useNavigate();
  const dispath = useDispatch();

  const modeCode = 'chat';

  const customHandleSend = async (content: string, attachments: FileItem[]) => {
    try {
      const params = {
        appCode: modeCode,
      };
      const res = await fetchNewChatId(params);
      const isOnlineState = getState().chat.isOnline;
      const stateData: QuestionsData = {
        content,
        isOnlineState,
        attachments: JSON.stringify(attachments),
      };
      if (res.data) {
        dispath({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: content.substring(0, 20) || '新对话',
            appCode: modeCode,
          },
        });

        navigate(`/chat/${res.data}?type=${modeCode}`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };

  const chatTopSlot = () => {
    return (
      <>
        <GreetingRender />
        <WeatherBannerRender />
      </>
    );
  };

  return (
    // <div className={styles.newChat}>
    //   <div className={styles.newChatWrap}>
    <div className={styles.homeChat}>
      <AIChat
        chatTopSlot={chatTopSlot()}
        customHandleSend={customHandleSend}
        showTemplateHeader={false}
      />
    </div>
    //   </div>
    // </div>
  );
};
export default ChatPage;
