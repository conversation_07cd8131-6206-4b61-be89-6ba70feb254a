/* 图片容器 */
.imageContainer {
  cursor: pointer;
  position: relative;
  width: 100%;
  // aspect-ratio: var(--aspect-ratio, 1/1);
  background-color: #f0f2f5;
  overflow: hidden;
  border-radius: 4px;
}

/* 图片样式 */
.galleryImage {
  width: 100%;
  height: auto; // 高度自适应
  display: block;
  object-fit: cover;
  transition: opacity 0.3s ease, transform 0.5s ease;
  transform-origin: center;
  border-radius: 4px;
}

.galleryImage.loading {
  opacity: 0;
}

.galleryImage.loaded {
  opacity: 1;
}

.galleryItem:hover .galleryImage.loaded {
  transform: scale(1.05);
}

/* 占位符样式 */
.imagePlaceholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(120deg, #f6f7f9 30%, #e9ebee 50%, #f6f7f9 70%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}
