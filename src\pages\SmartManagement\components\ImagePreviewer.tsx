import { ImagePreview } from '@douyinfe/semi-ui';
import { forwardRef, useImperativeHandle, useState } from 'react';

interface ImagePreviewerProps {
  mainUrl: string;
  allUrls?: string[];
}

const ImagePreviewer = forwardRef<{ open: () => void }, ImagePreviewerProps>(
  ({ mainUrl, allUrls }, ref) => {
    const [visible, setVisible] = useState(false);
    const [images, setImages] = useState<string[]>([]);

    useImperativeHandle(ref, () => ({
      open: () => {
        setImages(allUrls?.length ? allUrls : [mainUrl]);
        setVisible(true);
      },
    }));

    const handleClose = () => {
      setImages([]);
      setVisible(false);
    };

    return (
      <ImagePreview
        src={images}
        visible={visible}
        onClose={handleClose}
        onVisibleChange={setVisible}
        preLoad={true}
      />
    );
  },
);

ImagePreviewer.displayName = 'ImagePreviewer';
export default ImagePreviewer;
