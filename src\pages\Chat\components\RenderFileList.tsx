import Arrow from '@/assets/chat/arrow.svg';
import icodel from '@/assets/chat/icodel.svg';
import { Progress, Typography } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import styles from './index.less';

interface Props {
  fileList: any[];
  handleRemoveFile: (file: any) => void;
  isImageUploadDisabled: any;
}
/**
 * 渲染图片和文件滚动框
 * @param props
 * @returns
 */
const RenderFileList: React.FC<Props> = (props) => {
  const { handleRemoveFile, fileList, isImageUploadDisabled } = props;

  // 用于获取文件列表盒子宽度
  const fileBoxRef = useRef<HTMLDivElement>(null);

  // 用于获取父元素宽度
  const fileParentBoxRef = useRef<HTMLDivElement>(null);

  const [leftVisible, setLeftVisible] = useState(false);
  const [rightVisible, setRightVisible] = useState(false);
  const [x, setX] = useState(0);
  // 最大偏移 需动态，图片文件数量为动态
  const [maxX, setMaxX] = useState(0);
  // 最小偏移
  const minX = 0;
  // 图片宽度
  const imgWidth = 64;
  // 文件宽度
  const fileWidth = 208;

  // 点击向左滚动
  const handleClickLeft = () => {
    const width = !isImageUploadDisabled ? imgWidth : fileWidth;
    //  一次向左滚动两张的宽度 （xVal < 0）
    const xVal = x + width;

    if (xVal < minX) {
      setX(xVal);
    } else {
      // 占位
      setX(minX);
      setLeftVisible(false);
    }
    // 展示右侧按钮
    setRightVisible(true);
  };

  // 点击向右滚动
  const handleClickRight = () => {
    const width = !isImageUploadDisabled ? imgWidth : fileWidth;
    // 一次向左滚动两张的宽度
    const xVal = x - width;
    if (Math.abs(xVal) < maxX) {
      setX(xVal);
    } else {
      // 占位
      setX(0 - maxX);
      // 隐藏右侧按钮
      setRightVisible(false);
    }
    // 展示左侧按钮
    setLeftVisible(true);
  };

  const [fileBoxWidth, setFileBoxWidth] = useState(0);

  // 获取文件盒子宽度
  useLayoutEffect(() => {
    const element = fileBoxRef.current;
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const width = entry.contentRect.width;
          setFileBoxWidth(width);
        }
      });
      resizeObserver.observe(element);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  useEffect(() => {
    const parentEle = fileParentBoxRef.current;
    // parent width
    const parentWidth = parentEle?.offsetWidth || 0;

    // 父宽度大于内容宽度隐藏左右按钮
    if (parentWidth > fileBoxWidth) {
      setLeftVisible(false);
      setRightVisible(false);
      setX(0);
    }

    // 父宽度小于内容宽度展示右侧按钮
    if (parentWidth < fileBoxWidth) {
      // + 20 是为了让图片文件展示更全 不被按钮覆盖
      const maxXVal = fileBoxWidth - parentWidth + 20;
      setMaxX(maxXVal);
      // 偏移值为0 不处理
      if (x === minX) {
        setRightVisible(true);
        setLeftVisible(false);
        return;
      }

      // 新增不处理 只处理删除
      if (maxX < maxXVal) {
        setRightVisible(true);
        return;
      }

      // 滚动距离
      const slideGap = !isImageUploadDisabled ? imgWidth : fileWidth;

      // 删除文件后的偏移值
      const newX = x + slideGap;

      // 如果偏移值大于0 则重新计算偏移值
      if (newX > minX) {
        setX(minX);
        setLeftVisible(false);
      } else {
        setX(newX);
        if (Math.abs(newX) !== maxXVal) {
          setRightVisible(true);
        }
      }
    }
  }, [fileBoxWidth]);

  const handleDelete = (file: any) => {
    handleRemoveFile(file);
  };

  const { Title } = Typography;

  return (
    <div className={styles.customFileShowList} ref={fileParentBoxRef}>
      {leftVisible && (
        <div className={styles.customFileLeftArrow} onClick={handleClickLeft}>
          <img src={Arrow} style={{ transform: 'rotate(180deg)' }} />
        </div>
      )}
      <div
        className={styles.customFileList}
        ref={fileBoxRef}
        style={{ transform: `translateX(${x}px)` }}
      >
        {fileList.map((file: any) => (
          <div
            key={file.uid}
            className={classNames([
              file.type === 'image' ? styles.imageFileItem : styles.customFileItem,
              { [styles.fileProgress]: file.type !== 'image' && file.percent < 100 },
            ])}
            style={
              {
                '--upload-progress': `${file.percent}%`,
              } as React.CSSProperties
            }
          >
            {file.type === 'image' ? (
              <div className={styles.imageContainer}>
                {file.percent < 100 && (
                  <Progress
                    className={styles.imgFileProgress}
                    percent={file.percent}
                    type="circle"
                    size="small"
                    stroke="rgba(83, 183, 244, 60%)"
                    aria-label="disk usage"
                  />
                )}
                <img src={file.url} alt={file.name} className={styles.imagePreview} />
                <span className={styles.imageDelete} onClick={() => handleDelete(file)}>
                  <img src={icodel} alt="删除" />
                </span>
              </div>
            ) : (
              <>
                <div className={`${styles.fileIcon} ${styles[`fileIcon-${file.fileType}`]}`}>
                  {file.fileIcon}
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileNameRow}>
                    <Title
                      ellipsis={{ showTooltip: true }}
                      style={{
                        color: '#333',
                        fontSize: '14px',
                        fontWeight: 'normal',
                        lineHeight: '22px',
                      }}
                    >
                      {file.name}
                    </Title>
                    <span className={styles.fileDelete} onClick={() => handleRemoveFile(file)}>
                      <img src={icodel} alt="删除" />
                    </span>
                  </div>
                  <div className={styles.fileMetaRow}>
                    {file.percent < 100 ? (
                      <span className={styles.uploadFileStatus}>上传中... {file.percent}%</span>
                    ) : file?.parseStatus === 'pending' ? (
                      <span className={styles.uploadFileStatus}>解析中...</span>
                    ) : file?.parseStatus === 'error' ? (
                      <span className={styles.uploadFileStatus} style={{ color: '#FF0000' }}>
                        文件解析失败
                      </span>
                    ) : (
                      <span className={styles.fileType}>{file.fileType?.toUpperCase()}</span>
                    )}
                    {file.size && (
                      <>
                        <span className={styles.fileDot}>·</span>
                        <span className={styles.fileSize}>{file.size}</span>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        ))}
      </div>
      {rightVisible && (
        <div className={styles.customFileRightArrow} onClick={handleClickRight}>
          <img src={Arrow} />
        </div>
      )}
    </div>
  );
};

export default RenderFileList;
