.container {
  width: 800px;
  height: 292px;
  margin-top: 13px;
  display: flex;
  gap: 13px;
  padding: 0 4px;
  :global(.semi-image-preview-group) {
    display: none;
  }
  .uploadContainerWrapper {
    flex: 1;
    height: 100%;
    position: relative;
    cursor: pointer;
    border-radius: 16px;
    background: url('@/assets/svg/uploadbg.svg') !important;
    .uploadContainer {
      flex: 1;
      height: 100%;
      border-radius: 16px;
      :global {
        .semi-upload-add {
          width: 100%;
          height: 100%;
          background: none;
          border-radius: 16px !important;
        }
      }
    }
    .uploadContainerB {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      pointer-events: none;
      .uploadWrapper {
        width: 80%;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .uploadImages {
          width: 88px;
          height: 88px;
          margin-bottom: 8px;
        }
        .uploadTextt {
          font-family: PingFang SC;
          font-size: 14px;
          color: #999;
          text-align: center;
        }
      }
    }
  }
  .imageContainer {
    flex: 1;
    height: 100%;
    background: #f8faff;
    border-radius: 16px;
    border: 2px dashed #ebeef2;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .imageWrapper {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .uploadImage {
        width: 88px;
        height: 88px;
        margin-bottom: 8px;
      }
      .imageText {
        font-family: PingFang SC;
        font-size: 14px;
        color: #999;
      }
    }
    .swiperCarosel {
      width: 204px;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
    .imageContainerL {
      width: 40px;
      height: 40px;
      position: absolute;
      left: 16px;
      cursor: pointer;
    }
    .imageContainerR {
      width: 40px;
      height: 40px;
      position: absolute;
      right: 16px;
      transform: rotate(180deg);
      cursor: pointer;
    }
  }
}
.spinWrapper {
  :global(.semi-spin-wrapper) {
    color: #3498db !important;
  }
}

.processingState {
  border: 2px dashed #3498db !important;
  background-color: #f8faff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.processingText {
  margin-top: 12px;
  font-size: 14px;
  color: #3498db;
  text-align: center;
}

.uploadText {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  text-align: center;
}

.deleteButton {
  position: absolute;
  right: 16px;
  top: 16px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    // background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
  }

  img {
    width: 12px;
    height: 12px;
  }
}

.originImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  border: 2px dashed #ebeef2;
  border-radius: 16px;
  transition: all 0.2s ease;

  // &:hover {
  //   border-color: #3498db;
  //   transform: scale(1.02);
  // }
}

.processedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.2s ease;

  // &:hover {
  //   transform: scale(1.02);
  // }
}

.errorState {
  border: 2px dashed #ff4d4f !important;
  background-color: #fff2f0;
}

.errorText {
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
}
