{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
  "eslint.validate": ["javascript", "typescript", "less"],
  "stylelint.validate": ["css", "less", "scss"],
  "editor.codeActionsOnSave": {
    "source.fixAll.stylelint": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    ".env.dev": ".env.*",
    "README.md": "README*,CHANGELOG*,LICENSE,CNAME",
    "package.json": "pnpm-lock.yaml,yarn.lock,pnpm-workspace.yaml,.gitattributes,.gitignore,.gitpod.yml,.npmrc,.browserslistrc,.node-version,.git*,.tazerc.json,.editorconfig",
    ".eslintrc.js": ".prettierrc,.eslintignore,.prettierignore,.stylelintignore,.commitlintrc.*,.prettierrc.*,.stylelintrc.js,.lintstagedrc.mjs,cspell.json",
    "tailwind.config.js": "tailwind.css"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[svg]": {
    "editor.defaultFormatter": "jock.svg"
  },
  "[less]": {
    "editor.defaultFormatter": "Wscats.eno"
  }
}
