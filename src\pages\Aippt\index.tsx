import { getPPTCode } from '@/services/ppt';
import { Button, SideSheet } from '@douyinfe/semi-ui';
import React, { useEffect, useState } from 'react';

const AiPPT: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const change = () => {
    setVisible(!visible);
  };

  useEffect(() => {
    if (!visible && !document.getElementById('container')) return;
    (async () => {
      try {
        console.log(1111, document.getElementById('container'));

        const res = await getPPTCode();
        console.log(1111, res);
        await AipptIframe.show({
          appkey: '67b43bb7cdf54',
          channel: '',
          code: res.data as string,
          container: document.getElementById('container') as HTMLElement,
          editorModel: true,
          routerOptions: {
            list: ['workspace', 'generate', 'editor'],
            // 存在generate 并且存在templateScene的情况下 则进入选择模板页面时会自动选择字段并筛选 不存在则为全部模板
            generate: {
              templateScene: 1,
            },
          },
          options: {
            // 	功能板块配置(主要用于按钮的隐藏展示)
            fc_plate: [2001, 2003, 2011, 2014, 2024],
            // 自定义上传(可支持AI智能生成的标题，上传本地大纲下所有功能)
            custom_generate: {
              /**
             * 1: AI标题输入 (最大100字符)
              3: 上传Word
              4: 上传XMind
              5: 上传FreeMind
              6: 上传Markdown
              7: Markdown文本输入
              9: 上传PDF
              10: 上传TXT
              11: 自由文本输入 (收费规则: 上传Word付费)
              17: 上传参考文档
             */
              type: 7,
              step: 2,
              content: `# 故宫博物院古代绘画的VR展示方案
## 1. VR展示的前期准备
### 1.1 技术设备与平台搭建
#### 1.1.1 VR设备选择
- 选择高分辨率、低延迟的VR头盔，如HTC Vive或Oculus Rift，确保观众能够清晰地看到绘画细节，如《清明上河图》中人物的面部表情和动作。
- 配备高性能的计算机或服务器，以支持复杂的三维模型和高清图像的渲染，保证VR体验的流畅性，避免卡顿现象。
#### 1.1.2 展示平台开发
- 开发专门的VR展示平台，整合故宫博物院的绘画资源，提供用户友好的界面，方便观众浏览和选择不同的绘画作品。
- 在平台上设置多种交互功能，如放大缩小、旋转、标注等，让观众能够从不同角度欣赏绘画，深入了解作品的构图和色彩运用。
#### 1.1.3 内容采集与数字化
- 对故宫博物院的古代绘画进行高精度扫描和数字化处理，确保图像质量达到4K以上，保留绘画的每一个细节，如《千里江山图》中的青绿山水色彩层次。
- 利用三维建模技术，将绘画中的场景和元素进行立体化还原，为观众提供沉浸式的三维体验，如将《韩熙载夜宴图》中的宴会场景进行三维重建。
### 1.2 绘画作品的筛选与分类
#### 1.2.1 绘画风格分类
- 将绘画作品按照不同的风格进行分类，如写意画、工笔画、山水画、花鸟画等，方便观众根据自己的兴趣选择欣赏。
- 在每个风格类别中，选取具有代表性的作品进行重点展示，如写意画中的《墨葡萄图》，工笔画中的《簪花仕女图》。
#### 1.2.2 历史时期划分
- 按照绘画作品的创作年代进行划分，如唐宋时期、元明清时期等，展示不同时期绘画艺术的特点和发展脉络。
- 在每个历史时期中，选取具有代表性的画家和作品，如唐代的吴道子、宋代的范宽、明代的仇英等，通过他们的作品反映当时的社会风貌和艺术风格。
#### 1.2.3 文化主题归类
- 根据绘画作品所表达的文化主题进行归类，如宗教文化、宫廷文化、民间文化等，深入挖掘绘画背后的文化内涵。
- 对于具有相同文化主题的作品，进行集中展示和解读，如宗教文化中的《五百罗汉图》，宫廷文化中的《雍亲王题书堂深居图屏》。
## 2. VR展示的核心体验
### 2.1 沉浸式观赏体验
#### 2.1.1 三维场景还原
- 利用VR技术，将绘画作品中的场景进行三维还原，让观众仿佛置身于画中世界，如在《富春山居图》中漫步于山水之间，感受自然之美。
- 在三维场景中，添加动态元素，如流水、飞鸟、人物动作等，增强场景的真实感和生动性，使观众能够更好地融入其中。
#### 2.1.2 细节放大与标注
- 提供细节放大的功能，让观众可以近距离观察绘画作品的笔触、色彩、纹理等细节，如欣赏《清明上河图》时，能够清晰地看到街边店铺的招牌和人物的衣着细节。
- 在关键细节处添加标注和解说，为观众提供专业的艺术解读，如对《韩熙载夜宴图》中人物的身份、服饰含义进行标注，帮助观众更好地理解作品。
#### 2.1.3 艺术家虚拟讲解
- 邀请艺术专家或学者进行虚拟讲解，通过语音和视频的形式，为观众介绍绘画作品的历史背景、艺术价值和创作技法。
- 在讲解过程中，可以结合绘画作品的三维模型和细节展示，让观众更加直观地理解专家的讲解内容，如在讲解《千里江山图》时，通过三维模型展示其青绿山水的绘画技法。
### 2.2 互动式参与体验
#### 2.2.1 临摹与创作
- 提供临摹功能，让观众可以在虚拟环境中临摹绘画作品，感受中国传统绘画的笔法和墨韵，如临摹《兰亭序》的书法作品，体验书法的魅力。
- 设计简单的绘画创作工具，鼓励观众在虚拟空间中进行创作，将自己的创意融入到传统绘画风格中，如在虚拟画布上绘制一幅具有中国风的山水画。
#### 2.2.2 场景互动与探索
- 在三维场景中设置互动元素，如观众可以与画中的人物进行对话、与动物进行互动等，增加体验的趣味性和参与感，如在《韩熙载夜宴图》中与韩熙载进行虚拟对话。
- 允许观众自由探索绘画作品中的场景，发现隐藏的细节和故事，如在《清明上河图》中寻找特定的人物或事件，激发观众的好奇心和探索欲。
#### 2.2.3 虚拟社交与分享
- 在VR展示平台中加入社交功能，观众可以邀请朋友一起进入虚拟展厅，共同欣赏绘画作品，进行交流和讨论。
- 提供分享功能，观众可以将自己在VR展厅中的体验和创作分享到社交媒体，让更多的人了解故宫博物院的古代绘画艺术。
## 3. VR展示的教育与传播
### 3.1 教育课程与讲座
#### 3.1.1 专题教育课程
- 开发针对不同年龄段和知识水平的专题教育课程，如针对中小学生的中国传统绘画基础知识课程，针对大学生和艺术爱好者的绘画技法与鉴赏课程。
- 在课程中结合VR展示，通过生动的视觉体验和互动环节，提高教学效果，激发学生的学习兴趣，如在讲解绘画构图时，通过VR展示不同构图方式的绘画作品。
#### 3.1.2 学术讲座与研讨
- 定期举办学术讲座和研讨会，邀请艺术领域的专家学者，分享最新的研究成果和观点，如关于古代绘画的鉴定与修复技术、绘画与历史文化的关系等。
- 利用VR技术展示讲座中提到的绘画作品和相关案例，为观众提供更直观的理解，促进学术交流和知识传播。
#### 3.1.3 在线学习平台
- 建立在线学习平台，将教育课程和讲座内容进行录制和整理，供观众随时在线学习，打破时间和空间的限制。
- 在平台上设置学习进度跟踪和测试功能，帮助观众更好地掌握所学知识，提高学习效果。
### 3.2 文化传播与推广
#### 3.2.1 线上展览与推广
- 利用VR技术举办线上展览，将故宫博物院的古代绘画作品展示给全球观众，扩大文化传播的范围，让更多的人了解中国传统绘画艺术。
- 在线上展览中设置多种语言的解说和介绍，方便不同国家和地区的观众欣赏和理解绘画作品，如提供英文、日文、法文等多语种版本。
#### 3.2.2 合作与交流
- 与其他博物馆、艺术机构和文化企业开展合作，共同推广中国传统绘画艺术，如联合举办展览、开展文化交流活动等。
- 将VR展示技术应用于文化产品的开发，如开发以故宫绘画为主题的VR游戏、文创产品等，进一步拓展文化传播的渠道和方式。
#### 3.2.3 社会教育与公益
- 针对社会弱势群体和偏远地区的学生，开展公益性的VR展示活动，让他们也能享受到优质的文化教育资源，促进文化公平。
- 通过VR展示，培养公众对中国传统绘画艺术的兴趣和热爱，提高全民文化艺术素养，推动文化传承和发展。
## 4. VR展示的优化与创新
### 4.1 用户体验优化
#### 4.1.1 界面设计与操作便捷性
- 优化VR展示平台的界面设计，使其简洁、直观、易用，方便观众快速找到自己感兴趣的绘画作品和功能模块。
- 简化操作流程，降低用户的学习成本，如通过手势识别、语音控制等方式，让观众能够更加自然地与虚拟环境进行交互。
#### 4.1.2 性能优化与稳定性
- 持续优化VR展示的性能，减少加载时间和卡顿现象，提高展示的流畅性和稳定性，确保观众能够获得良好的体验。
- 定期对展示平台进行维护和更新，修复可能出现的漏洞和问题，及时更新绘画作品和相关内容，保持展示的新鲜感和吸引力。
#### 4.1.3 反馈与改进
- 建立用户反馈机制，及时收集观众的意见和建议，了解他们在使用VR展示过程中的体验和需求，如通过在线问卷、用户评论等方式。
- 根据用户反馈，对展示内容和功能进行持续改进和优化，不断提升用户体验，如根据观众的需求增加新的绘画作品或功能模块。
### 4.2 技术创新与拓展
#### 4.2.1 混合现实（MR）应用
- 探索混合现实技术在绘画展示中的应用，将虚拟绘画作品与现实场景相结合，创造出更加独特的视觉效果和互动体验，如在现实展厅中通过MR设备看到虚拟的绘画作品与周围环境的融合。
- 利用MR技术开发新的展示形式和互动方式，如观众可以在现实空间中与虚拟绘画作品进行互动，感受虚实结合的魅力。
#### 4.2.2 人工智能与大数据
- 结合人工智能技术，为观众提供个性化的推荐服务，根据观众的浏览历史和兴趣爱好，推荐相关的绘画作品和展示内容，提高观众的参与度和满意度。
- 利用大数据分析观众的行为数据和反馈信息，深入了解观众的需求和偏好，为展示内容的优化和创新提供依据，如根据观众对某类绘画风格的关注度，增加相关作品的展示。
#### 4.2.3 跨领域融合
- 探索VR展示与其他领域的融合，如与音乐、舞蹈、戏剧等艺术形式相结合，创造出更加丰富多样的文化体验，如在欣赏绘画作品的同时，聆听与之相关的古琴音乐，感受艺术的交融。
- 将VR展示技术应用于教育、旅游、娱乐等其他行业，拓展其应用范围和价值，如开发以故宫绘画为主题的VR旅游产品，让游客在虚拟环境中游览故宫并欣赏绘画作品。`,
            },
          },
          onMessage(eventType, data) {
            console.log(eventType, data);
          },
        });
      } catch (e) {
        console.log('🚀 ~ e:', e);
        /*
          如果初始化code信息失效，请在此处捕捉错误信息
          e: {
            code: number
            data: null
            msg: string | "code已过期"
          }
        */
      }
    })();
  }, [document.getElementById('container'), visible]);
  return (
    <>
      <Button onClick={change}>打开侧边栏</Button>
      <SideSheet
        title="Aippt"
        visible={visible}
        onCancel={change}
        className="w-full"
        width="100%"
        bodyStyle={{
          width: '100%',
        }}
      >
        {visible && <div id="container" className="h-full w-full"></div>}
      </SideSheet>
    </>
  );
};

export default AiPPT;
