<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="32" height="32" viewBox="0 0 32 32"><defs><mask id="master_svg0_536_08730" style="mask-type:alpha" maskUnits="objectBoundingBox"><g><ellipse cx="16" cy="16" rx="16" ry="16" fill="#FFFFFF" fill-opacity="1"/></g></mask><pattern x="0" y="0" width="32" height="32" patternUnits="userSpaceOnUse" id="master_svg1_536_06430"><image x="0" y="0" width="32" height="32" xlink:href="data:image/png;base64,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"/></pattern></defs><g mask="url(#master_svg0_536_08730)"><g><rect x="0" y="0" width="32" height="32" rx="0" fill="url(#master_svg1_536_06430)" fill-opacity="1"/></g></g></svg>