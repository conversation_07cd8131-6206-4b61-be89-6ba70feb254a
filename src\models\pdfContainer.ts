import { Reducer } from 'umi';

export interface PdfContainerModelState {
  url: '';
  name: '';
  size: '';
}

export interface PdfContainerModelType {
  namespace: 'pdfContainer';
  state: PdfContainerModelState;
  reducers: {
    changeUrl: Reducer<PdfContainerModelState>;
  };
}

const PdfContainerModel: PdfContainerModelType = {
  namespace: 'pdfContainer',

  state: {
    url: '',
    name: '',
    size: '',
  },
  reducers: {
    changeUrl(state, { payload }) {
      let s = {
        ...state } ;
      if(payload.url) s.url = payload.url ;
      if(payload.name) s.name = payload.name ;
      if(payload.size) s.size = payload.size ;
      return s ;
    },
  },
};

export default PdfContainerModel;
