import { Reducer } from 'umi';

export interface AiPPTModelState {
  visible: boolean;
  resId: string; // 消息唯一标识
  designId?: number | undefined; // ppt编辑id
  content?: string | undefined; // 大纲信息
  chatId: string | undefined;
}

export interface AiPPTModelType {
  namespace: 'aiPPT';
  state: AiPPTModelState;
  reducers: {
    onChange: Reducer<AiPPTModelState>;
    hidePpt: Reducer<AiPPTModelState>;
    clearPpt: Reducer<AiPPTModelState>;
  };
}

const PdfContainerModel: AiPPTModelType = {
  namespace: 'aiPPT',

  state: {
    visible: false,
    resId: '',
    designId: undefined,
    content: undefined,
    chatId: undefined,
  },
  reducers: {
    onChange(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    hidePpt(state) {
      return {
        ...state,
        visible: false,
      };
    },
    clearPpt(state) {
      return {
        ...state,
        resId: '',
        designId: undefined,
        content: undefined,
        chatId: undefined,
      };
    },
  },
};

export default PdfContainerModel;
