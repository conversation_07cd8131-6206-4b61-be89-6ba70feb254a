/**
 * 聊天操作按钮的点击效果处理
 * 为复制、喜欢和不喜欢按钮添加点击后的样式变化
 */

// 复制按钮的定时器ID
let copyButtonTimerId: number | null = null;

/**
 * 初始化聊天操作按钮的点击效果
 */
export function initChatActionButtons(): void {
  // 使用MutationObserver监听DOM变化，当新的聊天框添加时为其按钮添加事件监听
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        setupChatActionButtons();
      }
    });
  });

  // 开始观察文档变化
  observer.observe(document.body, { childList: true, subtree: true });

  // 初始设置
  setupChatActionButtons();
}

/**
 * 设置所有聊天操作按钮的点击事件
 */
function setupChatActionButtons(): void {
  // 查找所有聊天操作按钮
  const actionBars = document.querySelectorAll('.semi-chat-chatBox-action');

  actionBars.forEach((actionBar) => {
    const buttons = actionBar.querySelectorAll('button');

    // 为每个按钮设置点击事件，但避免重复绑定
    if (buttons.length >= 4) {
      // 设置复制按钮点击效果
      if (!buttons[0].hasAttribute('data-action-bound')) {
        buttons[0].setAttribute('data-action-bound', 'true');
        buttons[0].addEventListener('click', handleCopyButtonClick);
      }

      // 设置喜欢按钮点击效果
      if (!buttons[1].hasAttribute('data-action-bound')) {
        buttons[1].setAttribute('data-action-bound', 'true');
        buttons[1].addEventListener('click', (e) =>
          handleFeedbackButtonClick(e, buttons[1], buttons[2]),
        );
      }

      // 设置不喜欢按钮点击效果
      if (!buttons[2].hasAttribute('data-action-bound')) {
        buttons[2].setAttribute('data-action-bound', 'true');
        buttons[2].addEventListener('click', (e) =>
          handleFeedbackButtonClick(e, buttons[2], buttons[1]),
        );
      }
    }
  });
}

/**
 * 处理复制按钮点击
 * @param event 点击事件
 */
function handleCopyButtonClick(event: MouseEvent): void {
  const button = event.currentTarget as HTMLElement;

  // 添加点击效果类
  button.classList.add('clicked');

  // 如果已有定时器，清除它
  if (copyButtonTimerId !== null) {
    window.clearTimeout(copyButtonTimerId);
  }

  // 3秒后恢复原样
  copyButtonTimerId = window.setTimeout(() => {
    button.classList.remove('clicked');
    copyButtonTimerId = null;
  }, 3000);
}

/**
 * 处理喜欢和不喜欢按钮点击（互斥功能）
 * @param event 点击事件
 * @param currentButton 当前点击的按钮
 * @param oppositeButton 相对的按钮（喜欢/不喜欢的另一个）
 */
function handleFeedbackButtonClick(
  event: MouseEvent,
  currentButton: HTMLElement,
  oppositeButton: HTMLElement,
): void {
  // 如果当前按钮已经被选中，则取消选中
  if (currentButton.classList.contains('clicked')) {
    currentButton.classList.remove('clicked');
  } else {
    // 否则选中当前按钮，并确保取消另一个按钮的选中状态
    currentButton.classList.add('clicked');
    oppositeButton.classList.remove('clicked');
  }
}
