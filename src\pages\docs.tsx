/**
 * 知识库
 */
import { GreetingRender } from '@/pages/Chat/components/Greeting';
import { fetchNewChatId } from '@/services/chat';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { useDispatch, useNavigate } from 'umi';
import AIChat from './Chat/Chat';
import styles from './Chat/index.less';
import type { QuestionsData } from './Chat/types';
const DocsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispath = useDispatch();
  const customHandleSend = async (content: string, attachments: FileItem[]) => {
    try {
      const params = {
        appCode: 'kn-zsk', // 默认code，选择知识库后替换
      };
      const modeCode = 'kn-zsk';
      const res = await fetchNewChatId(params);
      const stateData: QuestionsData = {
        content,
        attachments,
      };
      if (res.data) {
        dispath({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: content.substring(0, 20) || '新对话',
            appCode: modeCode,
            source: '',
          },
        });

        navigate(`/chat/${res.data}?type=${params.appCode}&soucre=source`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };
  const chatTopSlot = () => {
    return (
      <>
        <GreetingRender />
        {/* <KnowledgeList /> */}
      </>
    );
  };
  return (
    <div className={styles.homeChat}>
      <AIChat
        chatTopSlot={chatTopSlot()}
        customHandleSend={customHandleSend}
        showTemplateHeader={false}
      />
    </div>
  );
};

export default () => <DocsPage />;
