.title {
  font-family: <PERSON>Fang SC;
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: normal;
  color: #000000;
}

.word {
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: normal;
  color: #999999;
  margin-top: 8px;
}

.container {
  width: 100%;
  overflow-y: auto;
  padding-bottom: 104px;
}
.wrapper {
  width: 800px;
  // height: 100%;
  // overflow-y: auto;
  margin: auto;
  // margin-top: 27px;
  // padding-bottom: 100px;
}

// 隐藏 Webkit 内核浏览器的滚动条
.container::-webkit-scrollbar {
  width: 0px;
  /* 宽度设为 0 隐藏滚动条 */
}

.cardTitleWrapper {
  height: 32px;
  display: flex;
  align-items: center;
}

.cardTitleIcon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.cardContent {
  width: 188px;
  height: 106px;
  padding: 12px;

  :global {
    .semi-card-body {
      padding: 0;
      margin: 0;
    }

    .semi-card-header {
      padding: 0;
      margin: 0;
    }
  }
}
