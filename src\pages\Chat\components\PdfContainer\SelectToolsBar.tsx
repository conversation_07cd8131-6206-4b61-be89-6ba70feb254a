import React,{ useRef,useState,useEffect } from 'react';
import styles from './index.less';

import { Toast,Popover } from '@douyinfe/semi-ui';
import { useSelector } from 'umi';
import { dispatchInUtils } from '@/utils';

import { IconChevronDown,IconChevronUp } from '@douyinfe/semi-icons';

import copyIcon from '@/assets/chatpdf/sel_btn_copy.svg';
import copySuccIcon from '@/assets/chatpdf/sel_btn_copy_succ.svg';
import transIcon from '@/assets/chatpdf/sel_btn_trans.svg';
import qIcon from '@/assets/chatpdf/sel_btn_q.svg';

import LangSelector from './LangSelector.tsx';

import eventBus from '@/utils/eventBus';

interface Props {
  top:number;
  left:number;
  content:string;
  chatid:string;
  mode:string;
  utrans:boolean;
  onLangSelectorShow: ()=>void;
  onLangSelectorHide: ()=>void;
}

const SelectToolsBar: React.FC<Props> = ({
  top=0,
  left=0,
  content='',
  chatid='',
  mode='',
  utrans=true,
  onLangSelectorShow,
  onLangSelectorHide,
}: Props) => {

  const [copyed, setCopyed] = useState(false);
  const copyTimer = useRef(null);

  const [langSelectorVisible, setLangSelectorVisible] = useState(false);

  const [sourceLang, setSourceLang] = useState({ id:'auto',name:'自动识别',enname:'' });
  const [targetLang, setTargetLang] = useState({ id:'en',name:'English',enname:'English' });


  async function clickToCopy(){
    try {
      await navigator.clipboard.writeText(content);

      Toast.success({
        content: '复制成功',
        duration: 1.5,
      });

      setCopyed(true);

      copyTimer.current && clearTimeout(copyTimer.current);
      copyTimer.current = setTimeout(()=>{
        setCopyed(false);
      },1500);
    } catch {
      Toast.error('请手动复制');
    }
  };

  function onLangsChange(ddata){
    if(ddata.type == 'target') setTargetLang(ddata.data) ;
    else if(ddata.type == 'source') setSourceLang(ddata.data) ;


  }

  function onLangSelectorVisibleChange(e){
    if(e) onLangSelectorShow && onLangSelectorShow();
    else onLangSelectorHide && onLangSelectorHide();
  }

  function onToSelectLangBtnClick(e){
    e.stopPropagation();
    setLangSelectorVisible(!langSelectorVisible);
  }

  const { chatMsg } = useSelector((state: { chat: ChatModelState }) => state.chat);

  const chatContainerHide: boolean = useSelector(
    (state: { pageLayout: { chatContainerHide: false } }) => state.pageLayout.chatContainerHide,
  );

  const transTimer = useRef(null);
  function onClickToTrans(){
    if(content.trim() == '') return ;

    if(transTimer.current || chatMsg![chatid]?.pending){
      Toast.error('上个问题正在处理中，请稍后');
      return ;
    }

    transTimer.current = true ;

    setTimeout(()=>{
      transTimer.current = false ;
    },3000);

    let ucontent = content + '翻译为' + targetLang.name ;
    eventBus.emit('Chat-Chat-HandleMessageSend',{
      content : ucontent,
    });

    if(chatContainerHide){
      dispatchInUtils({
        type: 'pageLayout/changeChatContainerHide',
        payload: false,
      });
    }
  }

  return (
	<div className={styles.pdfContainerSelectToolsBar} style={{left,top}}>
    {mode == 'trans' || !utrans  ? (<div className={styles.copyBtn} onClick={clickToCopy}>
      {copyed ? <img src={copySuccIcon} className={styles.icon} /> : <img src={copyIcon} className={styles.icon} />}
      复制
    </div>) : <>

      <div className={styles.transBtn}>
        <img src={transIcon} className={styles.icon} />
        <div  className={styles.transTxtBtn} onClick={onClickToTrans}>翻译</div>

        <Popover
        	trigger="custom"
        	position="bottomRight"
        	visible={langSelectorVisible}
        	onVisibleChange={onLangSelectorVisibleChange}
        	content={<LangSelector
            showOriBtn={false} onLangsChange={onLangsChange}
            sourceLang={sourceLang}
            targetLang={targetLang}
          />}
        >
        	<div className={styles.toTransBtn} onClick={onToSelectLangBtnClick}>
            {langSelectorVisible ? <IconChevronUp size="small" /> : <IconChevronDown size="small" />}
        	</div>
        </Popover>

      </div>

      <div className={styles.copyBtn} onClick={clickToCopy}>
        {copyed ? <img src={copySuccIcon} className={styles.icon} /> : <img src={copyIcon} className={styles.icon} />}
        复制
      </div>

      {false && <div className={styles.qBtn}>
        <img src={qIcon} className={styles.icon} />
        引用
      </div>}

    </>}

	</div>
  );
};

export default SelectToolsBar;
