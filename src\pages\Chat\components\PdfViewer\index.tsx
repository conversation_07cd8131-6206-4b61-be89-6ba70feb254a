import closeIcon from '@/assets/chatpdf/btn_close.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import { dispatchInUtils } from '@/utils';
import classNames from 'classnames';
import { FC, useRef } from 'react';
import styled from './index.less';

interface Props {
  name: string;
  url: string;
  isRoute?: boolean;
}

const PdfViewer: FC<Props> = ({ name, url, isRoute = false }) => {
  const iframeRef = useRef<any>();
  const handleToggleButton = () => {
    const iframeDocument = iframeRef.current?.contentWindow.document;
    iframeDocument.getElementById('sidebarToggleButton')?.click();
  };
  const handlePresentationMode = () => {
    const iframeDocument = iframeRef.current?.contentWindow.document;
    iframeDocument.getElementById('presentationMode')?.click();
  };
  // const downloadButton = () => {
  //   const iframeDocument = iframeRef.current?.contentWindow.document;
  //   console.log(455, iframeDocument);

  //   iframeDocument.getElementById('downloadButton').click();
  // };
  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });
  };

  return (
    <div className={styled.pdfViewer}>
      <div className={classNames(styled.pdfViewerToolsBar, { [styled.isRoute]: isRoute })}>
        <div className={styled.btnThumbControl} onClick={handleToggleButton}>
          <img src={thumbControlIcon} />
        </div>
        <div className={styled.title}>{name}</div>
        <div className={styled.btnToBig} onClick={handlePresentationMode}>
          <img src={toBigIcon} />
        </div>
        {/* 产品同步去掉 */}
        {/* {isRoute && (
          <div
            className={styled.btnToBig}
            onClick={() => downloadFile(url, `${name || '文件'}.pdf`)}
          >
            <DownloadIcon />
          </div>
        )} */}
        {!isRoute && (
          <div className={styled.btnClose} onClick={handleClose}>
            <img src={closeIcon} />
          </div>
        )}
      </div>
      <iframe
        ref={iframeRef}
        className={styled.pdfIframe}
        src={`/pdfjs-dist/web/viewer.html?file=${url}`}
      />
    </div>
  );
};
export default PdfViewer;
