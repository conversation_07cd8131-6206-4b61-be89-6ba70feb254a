import { CloseIcon } from '@/assets/svg';
import { AiPPTModelState } from '@/models/aiPPT';
import { AuthModelState } from '@/models/auth';
import { fetchAiPptByChatIdAndResId, fetchAiPptSave, getPPTCode } from '@/services/ppt';
import { Modal, Toast } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import { FC, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styled from './index.less';

const IframePPT: FC = () => {
  const { visible, resId, designId, content, chatId } = useSelector(
    (state: { aiPPT: AiPPTModelState }) => state.aiPPT,
  );
  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);
  const dispatch = useDispatch();
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [isDisabled, setIsDisable] = useState<boolean>(false); // 制作中不可关闭
  const [isCallBack, setIsCallBack] = useState<boolean>(false); // 是否触发关闭回调
  const clearPPt = () => {
    setIsDisable(false);
    setIsCallBack(false);
    dispatch({
      type: 'aiPPT/hidePpt',
    });
    dispatch({ type: 'aiPPT/clearPpt' });
  };
  useEffect(() => {
    return () => clearPPt();
  }, []);

  useEffect(() => {
    if (!visible) return;
    setIsEdit(!!designId);

    const params: any = {
      appkey: process.env.PPT_APP_KEY,
      channel: '',
      container: document.getElementById('container') as HTMLElement,
      editorModel: true,
      isShowBack: false,
      routerOptions: {
        list: ['generate', 'editor'],
        // 存在generate 并且存在templateScene的情况下 则进入选择模板页面时会自动选择字段并筛选 不存在则为全部模板
        // generate: {
        //   templateScene: 1,
        // },
        editor: {
          id: designId,
        },
      },
    };
    if (content) {
      params.options = {
        fc_plate: [2014, 2024],
        // 自定义上传(可支持AI智能生成的标题，上传本地大纲下所有功能)
        custom_generate: {
          /**
           * 1: AI标题输入 (最大100字符)
            3: 上传Word
            4: 上传XMind
            5: 上传FreeMind
            6: 上传Markdown
            7: Markdown文本输入
            9: 上传PDF
            10: 上传TXT
            11: 自由文本输入 (收费规则: 上传Word付费)
            17: 上传参考文档
           */
          type: 7,
          step: 2,
          content: content,
        },
      };
    }
    getPPTCode().then(async (res) => {
      try {
        await AipptIframe.show({
          ...params,
          // code: 'a164e7be970d0046d20b377f6b590ea8',
          href: process.env.AI_PPT_IFRAME_URL,
          code: res.data as string,
          onMessage(eventType, data) {
            console.log(111, eventType, data);

            if (eventType === 'ROUTER') {
              setIsEdit(data.to === 'editor');
            } else if (eventType === 'TEMPLATE_SELECTED') {
              setIsDisable(true);
            }
            // else if (eventType === 'SET_PPT_MAKING_STATUS') {
            //   setIsDisable(data.status === '1');
            // }
            // PPT已生成
            if (eventType === 'GENERATE_PPT_SUCCESS' || eventType === 'PPT_SAVE') {
              setIsDisable(false);
              setIsCallBack(true);
              const params = {
                userId: user?.id,
                userDesignId: data?.id,
                taskId: data?.taskId,
                title: data?.title || data?.filename,
                thumbnail: data?.thumbnail,
                chatId,
                chatResId: resId,
              };
              fetchAiPptSave(params).then((res) => {
                // 更新相应message封面图、id
                dispatch({
                  type: 'chat/updatePptInfo',
                  payload: {
                    chatId,
                    resId,
                    pptDetail: {
                      thumbnail: data?.thumbnail || res.data?.thumbnail || '',
                      userDesignId: data?.id,
                      title: params.title,
                    },
                  },
                });
              });
            }
          },
        });
      } catch (e: any) {
        Toast.error(e.message);
        setTimeout(() => {
          clearPPt();
        }, 1000);
      }
    });
  }, [visible]);

  const pollApi = () => {
    let intervalId = setInterval(() => {
      fetchAiPptByChatIdAndResId({
        chatId,
        chatResId: resId,
      })
        .then((res: any) => {
          const { data = {}, code } = res;
          if (code !== 0) {
            clearInterval(intervalId);
          }
          if (data?.genStatus === 1) {
            clearInterval(intervalId);
            dispatch({
              type: 'chat/updatePptInfo',
              payload: {
                chatId,
                resId,
                pptDetail: {
                  taskId: data?.taskId,
                  thumbnail: data?.thumbnail,
                  userDesignId: data?.userDesignId,
                  title: data?.title,
                  pdfUrl: data?.pdfUrl,
                  pptUrl: data?.pptUrl,
                },
              },
            });
          } else if (data?.genStatus === 2) {
            Toast.error('ppt生成异常');
            clearInterval(intervalId);
          }
        })
        .catch(() => {
          // 出错时也可以选择停止轮询
          clearInterval(intervalId);
        });
    }, 2000); // 两秒钟调用一次
  };

  // 关闭AIPPT
  const hidePpt = () => {
    if (isDisabled) return;
    // 更新message数据
    if (isCallBack) {
      fetchAiPptByChatIdAndResId({
        chatId,
        chatResId: resId,
      }).then((res: any) => {
        const { data = {}, code } = res;
        if (code !== 0) return;
        if (data?.genStatus === 1) {
          dispatch({
            type: 'chat/updatePptInfo',
            payload: {
              chatId,
              resId,
              pptDetail: {
                taskId: data?.taskId,
                thumbnail: data?.thumbnail,
                userDesignId: data?.userDesignId,
                title: data?.title,
                pdfUrl: data?.pdfUrl,
                pptUrl: data?.pptUrl,
              },
            },
          });
        } else {
          pollApi();
        }
      });
    }
    dispatch({
      type: 'aiPPT/hidePpt',
    });
    dispatch({ type: 'aiPPT/clearPpt' });
  };

  return (
    <Modal
      modalContentClass={styled['iframe-content']}
      header={null}
      footer={null}
      fullScreen
      visible={visible}
      closeOnEsc={false}
    >
      {isEdit && <div className={styled.block} />}
      <div
        className={classNames(styled.close, isEdit ? styled.editor : styled['template-select'])}
        onClick={hidePpt}
      >
        <CloseIcon className={styled['close-icon']} />
      </div>
      <div id="container" className="h-full w-full"></div>
    </Modal>
  );
};

export default IframePPT;
