import { useCallback, useEffect, useRef } from 'react';

const useInfiniteScroll = (
  loadMore: () => void,
  hasMore: boolean,
  isLoading: boolean,
  options?: {
    rootMargin?: string;
    threshold?: number;
    debounceMs?: number;
  },
) => {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const { rootMargin = '100px 0px', threshold = 0.1, debounceMs = 100 } = options || {};

  const debouncedLoadMore = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      if (hasMore && !isLoading) {
        loadMore();
      }
    }, debounceMs);
  }, [loadMore, hasMore, isLoading, debounceMs]);

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry.isIntersecting) {
        debouncedLoadMore();
      }
    },
    [debouncedLoadMore],
  );

  useEffect(() => {
    if (!triggerRef.current) return;

    observerRef.current = new IntersectionObserver(handleObserver, {
      rootMargin,
      threshold,
    });

    observerRef.current.observe(triggerRef.current);

    return () => {
      observerRef.current?.disconnect();
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [handleObserver, rootMargin, threshold]);

  return { triggerRef };
};

export default useInfiniteScroll;