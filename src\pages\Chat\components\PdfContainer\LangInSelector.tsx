import React from 'react';
import styles from './index.less';

import checkIcon from '@/assets/chatpdf/icon_check.svg';

interface Props {
  selectedLang: object;
  onSelected: () => void;
  uAutoItem: boolean;
}

const LangInSelector: React.FC<Props> = ({ selectedLang, onSelected, uAutoItem }: Props) => {
  const langs = [
    {
      id: 'zh',
      name: '中文',
      enname: 'Chinese',
    },
    {
      id: 'en',
      name: 'English',
      enname: 'English',
    },
  ];

  if (uAutoItem)
    langs.unshift({
      id: 'auto',
      name: '自动识别',
      enname: '',
    });

  return (
    <div className={styles.langSelectorInWrap}>
      {langs.map((langItem) => {
        return (
          <div
            key={langItem.id}
            className={styles.langSelectorInItem}
            onClick={(e) => {
              e.stopPropagation();
              onSelected && onSelected(langItem, e);
            }}
          >
            <div className={styles.langSelectorInIteml}>
              <div className={styles.langSelectorInItemTitle}>{langItem.name}</div>
              <div className={styles.langSelectorInItemTip}>{langItem.enname}</div>
            </div>
            {selectedLang && selectedLang.id == langItem.id ? (
              <img src={checkIcon} className={styles.icon} />
            ) : (
              ''
            )}
          </div>
        );
      })}
    </div>
  );
};

export default LangInSelector;
