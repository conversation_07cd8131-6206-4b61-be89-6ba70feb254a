import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

import { useSelector } from 'umi';

import { IconChevronDown, IconChevronUp } from '@douyinfe/semi-icons';
import { Input, Popover } from '@douyinfe/semi-ui';

import { dispatchInUtils } from '@/utils';

import closeIcon from '@/assets/chatpdf/btn_close.svg';
import toSmallIcon from '@/assets/chatpdf/btn_small_viewer.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import transIcon from '@/assets/chatpdf/btn_trans.svg';
import transCloseIcon from '@/assets/chatpdf/btn_trans_close.svg';

import LangSelector from './LangSelector.tsx';

interface Props {
  title: string;
  onBtnClick: () => void;
  onLangsChange: () => void;
  showOriPdf: boolean;
  transMode: boolean;
  sourceLang: object;
  targetLang: object;
  utrans: boolean;
  pagenum: string;
  pagesum: string;
  onPagerInputChange: () => void;
  onPagerInputEnter: () => void;
}

const TopBar: React.FC<Props> = ({
  title,
  showOriPdf,
  transMode,
  onBtnClick,
  onLangsChange,
  sourceLang,
  targetLang,
  utrans = true,
  pagenum = '',
  pagesum = '',
  onPagerInputChange,
  onPagerInputEnter,
}: Props) => {
  const [langSelectorVisible, setLangSelectorVisible] = useState(false);
  const pageInputerRef = useRef(null);

  useEffect(() => {
    dispatchInUtils({
      type: 'pageLayout/changeTransState',
      payload: {
        showtrans: false,
        transhowori: showOriPdf,
      },
    });
  }, []);

  function onShowOriPdfChange(e) {
    onBtnClick({
      action: 'showOriPdfChange',
      data: e,
    });
  }

  function clickToTransMode(e) {
    onBtnClick({
      action: 'changeTransMode',
      data: e,
    });
  }

  function onBodyClick(e) {
    let findit = false;
    let tnode = e.target;
    while (tnode) {
      if (tnode === 'body') break;

      if (tnode && tnode.className && tnode.className.indexOf('semi-popover') > -1) {
        findit = true;
        break;
      }

      tnode = tnode.parentNode;
      if (!tnode) break;
    }

    if (findit) return;

    setLangSelectorVisible(false);
    document.body.removeEventListener('click', onBodyClick);
    return;
  }

  function onLangSelectorVisibleChange(e) {
    if (e) {
      document.body.addEventListener('click', onBodyClick);
    }
  }

  const chatContainerHide: string = useSelector(
    (state: { pageLayout: { chatContainerHide: '' } }) => state.pageLayout.chatContainerHide,
  );

  function clickToBigScreen() {
    dispatchInUtils({
      type: 'pageLayout/changeChatContainerHide',
      payload: !chatContainerHide,
    });
  }

  function clickToClose() {
    dispatchInUtils({
      type: 'pageLayout/changeChatContainerHide',
      payload: '',
    });

    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });

    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });

    dispatchInUtils({
      type: 'pageLayout/changeTransState',
      payload: {
        showtrans: false,
        transhowori: showOriPdf,
      },
    });
  }

  function onPagerInputEnterPress() {
    if (pageInputerRef.current) {
      (pageInputerRef.current as HTMLInputElement).blur();
    }
  }

  return (
    <div className={styles.pdfContainerToolsBar}>
      {false && (
        <div className={styles.btnThumbControl}>
          <img src={thumbControlIcon} />
        </div>
      )}

      {false && (
        <div className={styles.pageselector}>
          <Input
            ref={(ref) => (pageInputerRef.current = ref)}
            value={pagenum}
            className={styles.pageselectorinput}
            onChange={onPagerInputChange}
            onEnterPress={onPagerInputEnterPress}
            onBlur={onPagerInputEnter}
            size="small"
          ></Input>
          /<span className={styles.pageselectornum}>{pagesum ? pagesum : '..'}</span>
        </div>
      )}

      <div className={styles.title} style={utrans ? {} : { textAlign: 'center' }}>
        {title}
      </div>

      {utrans && (
        <>
          <div className={styles.transBtn}>
            <div className={styles.transInBtn} onClick={clickToTransMode}>
              {transMode ? (
                <img src={transCloseIcon} className={styles.icon} />
              ) : (
                <img src={transIcon} className={styles.icon} />
              )}
              {transMode ? '关闭翻译' : '翻译全文'}
            </div>

            <Popover
              trigger="custom"
              position="bottomRight"
              visible={langSelectorVisible}
              onVisibleChange={onLangSelectorVisibleChange}
              content={
                <LangSelector
                  showOriPdf={showOriPdf}
                  onShowOriPdfChange={onShowOriPdfChange}
                  onLangsChange={onLangsChange}
                  sourceLang={sourceLang}
                  targetLang={targetLang}
                />
              }
            >
              <div
                className={styles.langSelectorBtn}
                onClick={(e) => {
                  e.stopPropagation();
                  setLangSelectorVisible(!langSelectorVisible);
                }}
              >
                {langSelectorVisible ? <IconChevronUp /> : <IconChevronDown />}
              </div>
            </Popover>
          </div>

          <div className={styles.flex1}></div>
        </>
      )}
      <div className={styles.btnToBig} onClick={clickToBigScreen}>
        {chatContainerHide ? <img src={toSmallIcon} /> : <img src={toBigIcon} />}
      </div>
      <div className={styles.btnClose} onClick={clickToClose}>
        <img src={closeIcon} />
      </div>
    </div>
  );
};

export default TopBar;
