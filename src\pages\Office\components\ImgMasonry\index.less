.masonryGridColumn {
  margin-bottom: 8px;
  padding-left: 8px;
  background-clip: padding-box;
}

.masonryGridColumn > div {
  margin-bottom: 8px;
}

.imgMasonry {
  margin-top: 8px;
}

.masonryGrid {
  display: box;
  display: flexbox;
  display: flex;
  margin-left: -8px;
  width: auto;
  padding: 0 2px;
}

/* 图片项目 */
.galleryItem {
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.25s;
  box-shadow: 0 2px 8px rgba(0,0,0,4%);
  background: #fff;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-2px) scale(1.01);
    // z-index: 1;
  }

  .imageActions {
  visibility: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  display: flex;
  align-items: end;
  justify-content: end;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0%) 24%, rgba(0, 0, 0, 41%) 39%, #000 100%);

  .actionBtn {
    position: relative;
    z-index: 999;
    width: 32px;
    height: 32px;
    margin: 0 12px 12px;
    border-radius: 4px;
    color: #fff;
    background: url('@/assets/txt2img/action-bg.svg') no-repeat 100% 100%;
  }

  .actionBtn:first-child {
    margin-right: 0;
  }

  :global(.semi-button-content-right) {
    font-size: 14px !important;
    font-weight: normal !important;
  }
}

}

.galleryItem:hover .imageActions {
  visibility: visible;
}