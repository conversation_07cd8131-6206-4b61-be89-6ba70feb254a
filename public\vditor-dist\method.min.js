!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Vditor=t():e.Vditor=t()}(this,(()=>(()=>{"use strict";var e={913:(e,t,n)=>{n.d(t,{Y:()=>a});var a=function(){function e(){}return e.ZWSP="​",e.DROP_EDITOR="application/editor",e.MOBILE_WIDTH=520,e.CLASS_MENU_DISABLED="vditor-menu--disabled",e.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],e.CODE_THEME=["a11y-dark","agate","an-old-hope","androidstudio","arta","atom-one-dark","atom-one-dark-reasonable","base16/3024","base16/apathy","base16/apprentice","base16/ashes","base16/atelier-cave","base16/atelier-dune","base16/atelier-estuary","base16/atelier-forest","base16/atelier-heath","base16/atelier-lakeside","base16/atelier-plateau","base16/atelier-savanna","base16/atelier-seaside","base16/atelier-sulphurpool","base16/atlas","base16/bespin","base16/black-metal","base16/black-metal-bathory","base16/black-metal-burzum","base16/black-metal-dark-funeral","base16/black-metal-gorgoroth","base16/black-metal-immortal","base16/black-metal-khold","base16/black-metal-marduk","base16/black-metal-mayhem","base16/black-metal-nile","base16/black-metal-venom","base16/brewer","base16/bright","base16/brogrammer","base16/brush-trees-dark","base16/chalk","base16/circus","base16/classic-dark","base16/codeschool","base16/colors","base16/danqing","base16/darcula","base16/dark-violet","base16/darkmoss","base16/darktooth","base16/decaf","base16/default-dark","base16/dracula","base16/edge-dark","base16/eighties","base16/embers","base16/equilibrium-dark","base16/equilibrium-gray-dark","base16/espresso","base16/eva","base16/eva-dim","base16/flat","base16/framer","base16/gigavolt","base16/google-dark","base16/grayscale-dark","base16/green-screen","base16/gruvbox-dark-hard","base16/gruvbox-dark-medium","base16/gruvbox-dark-pale","base16/gruvbox-dark-soft","base16/hardcore","base16/harmonic16-dark","base16/heetch-dark","base16/helios","base16/hopscotch","base16/horizon-dark","base16/humanoid-dark","base16/ia-dark","base16/icy-dark","base16/ir-black","base16/isotope","base16/kimber","base16/london-tube","base16/macintosh","base16/marrakesh","base16/materia","base16/material","base16/material-darker","base16/material-palenight","base16/material-vivid","base16/mellow-purple","base16/mocha","base16/monokai","base16/nebula","base16/nord","base16/nova","base16/ocean","base16/oceanicnext","base16/onedark","base16/outrun-dark","base16/papercolor-dark","base16/paraiso","base16/pasque","base16/phd","base16/pico","base16/pop","base16/porple","base16/qualia","base16/railscasts","base16/rebecca","base16/ros-pine","base16/ros-pine-moon","base16/sandcastle","base16/seti-ui","base16/silk-dark","base16/snazzy","base16/solar-flare","base16/solarized-dark","base16/spacemacs","base16/summercamp","base16/summerfruit-dark","base16/synth-midnight-terminal-dark","base16/tango","base16/tender","base16/tomorrow-night","base16/twilight","base16/unikitty-dark","base16/vulcan","base16/windows-10","base16/windows-95","base16/windows-high-contrast","base16/windows-nt","base16/woodland","base16/xcode-dusk","base16/zenburn","codepen-embed","dark","devibeans","far","felipec","github-dark","github-dark-dimmed","gml","gradient-dark","hybrid","ir-black","isbl-editor-dark","kimbie-dark","lioshi","monokai","monokai-sublime","night-owl","nnfx-dark","nord","obsidian","panda-syntax-dark","paraiso-dark","pojoaque","qtcreator-dark","rainbow","shades-of-purple","srcery","stackoverflow-dark","sunburst","tomorrow-night-blue","tomorrow-night-bright","tokyo-night-dark","vs2015","xt256","ant-design","a11y-light","arduino-light","ascetic","atom-one-light","base16/atelier-cave-light","base16/atelier-dune-light","base16/atelier-estuary-light","base16/atelier-forest-light","base16/atelier-heath-light","base16/atelier-lakeside-light","base16/atelier-plateau-light","base16/atelier-savanna-light","base16/atelier-seaside-light","base16/atelier-sulphurpool-light","base16/brush-trees","base16/classic-light","base16/cupcake","base16/cupertino","base16/default-light","base16/dirtysea","base16/edge-light","base16/equilibrium-gray-light","base16/equilibrium-light","base16/fruit-soda","base16/github","base16/google-light","base16/grayscale-light","base16/gruvbox-light-hard","base16/gruvbox-light-medium","base16/gruvbox-light-soft","base16/harmonic16-light","base16/heetch-light","base16/humanoid-light","base16/horizon-light","base16/ia-light","base16/material-lighter","base16/mexico-light","base16/one-light","base16/papercolor-light","base16/ros-pine-dawn","base16/sagelight","base16/shapeshifter","base16/silk-light","base16/solar-flare-light","base16/solarized-light","base16/summerfruit-light","base16/synth-midnight-terminal-light","base16/tomorrow","base16/unikitty-light","base16/windows-10-light","base16/windows-95-light","base16/windows-high-contrast-light","brown-paper","base16/windows-nt-light","color-brewer","docco","foundation","github","googlecode","gradient-light","grayscale","idea","intellij-light","isbl-editor-light","kimbie-light","lightfair","magula","mono-blue","nnfx-light","panda-syntax-light","paraiso-light","purebasic","qtcreator-light","routeros","school-book","stackoverflow-light","tokyo-night-light","vs","xcode","default"],e.ALIAS_CODE_LANGUAGES=["abc","plantuml","mermaid","flowchart","echarts","mindmap","graphviz","math","markmap","smiles","js","ts","html","toml","c#","bat"],e.CDN="https://unpkg.com/vditor@".concat("3.10.9"),e.MARKDOWN_OPTIONS={autoSpace:!1,gfmAutoLink:!0,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",linkPrefix:"",listStyle:!1,mark:!1,mathBlockPreview:!0,paragraphBeginningSpace:!1,sanitize:!0,toc:!1},e.HLJS_OPTIONS={enable:!0,lineNumber:!1,defaultLang:"",style:"github"},e.MATH_OPTIONS={engine:"KaTeX",inlineDigit:!1,macros:{}},e.THEME_OPTIONS={current:"light",list:{"ant-design":"Ant Design",dark:"Dark",light:"Light",wechat:"WeChat"},path:"".concat(e.CDN,"/dist/css/content-theme")},e}()},931:(e,t,n)=>{n.d(t,{Y:()=>s});var a=n(913),r=n(161),i=n(59),o=n(933),s=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var s=i.SMILESRenderAdapter.getElements(e);s.length>0&&(0,r.Z)("".concat(t,"/dist/js/smiles-drawer/smiles-drawer.min.js?v=2.1.7"),"vditorAbcjsScript").then((function(){var e=new SmiDrawer({},{});s.forEach((function(t){var a=i.SMILESRenderAdapter.getCode(t).trim();if("true"!==t.getAttribute("data-processed")&&""!==a.trim()){var r="smiles"+(0,o.Ee)();t.innerHTML='<svg id="'.concat(r,'"></svg>'),e.draw(a,"#"+r,"dark"===n?"dark":void 0),t.setAttribute("data-processed","true")}}))}))}},288:(e,t,n)=>{n.d(t,{$:()=>o});var a=n(913),r=n(161),i=n(59),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var n=i.abcRenderAdapter.getElements(e);n.length>0&&(0,r.Z)("".concat(t,"/dist/js/abcjs/abcjs_basic.min.js"),"vditorAbcjsScript").then((function(){n.forEach((function(e){e.parentElement.classList.contains("vditor-wysiwyg__pre")||e.parentElement.classList.contains("vditor-ir__marker--pre")||"true"!==e.getAttribute("data-processed")&&(ABCJS.renderAbc(e,i.abcRenderAdapter.getCode(e).trim()),e.style.overflowX="auto",e.setAttribute("data-processed","true"))}))}))}},59:(e,t,n)=>{n.r(t),n.d(t,{SMILESRenderAdapter:()=>r,abcRenderAdapter:()=>l,chartRenderAdapter:()=>c,flowchartRenderAdapter:()=>u,graphvizRenderAdapter:()=>d,markmapRenderAdapter:()=>o,mathRenderAdapter:()=>a,mermaidRenderAdapter:()=>i,mindmapRenderAdapter:()=>s,plantumlRenderAdapter:()=>m});var a={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-math")}},r={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-smiles")}},i={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-mermaid")}},o={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-markmap")}},s={getCode:function(e){return e.getAttribute("data-code")},getElements:function(e){return e.querySelectorAll(".language-mindmap")}},c={getCode:function(e){return e.innerText},getElements:function(e){return e.querySelectorAll(".language-echarts")}},l={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-abc")}},d={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-graphviz")}},u={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-flowchart")}},m={getCode:function(e){return e.textContent},getElements:function(e){return e.querySelectorAll(".language-plantuml")}}},784:(e,t,n)=>{n.d(t,{v:()=>l});var a=n(913),r=n(161),i=n(59),o=n(933),s=function(e,t,n,a){return new(n||(n=Promise))((function(r,i){function o(e){try{c(a.next(e))}catch(e){i(e)}}function s(e){try{c(a.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((a=a.apply(e,t||[])).next())}))},c=function(e,t){var n,a,r,i,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(o=0)),o;)try{if(n=1,a&&(r=2&s[0]?a.return:s[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,s[1])).done)return r;switch(a=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,a=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){o.label=s[1];break}if(6===s[0]&&o.label<r[1]){o.label=r[1],r=s;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(s);break}r[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],a=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},l=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var l=i.chartRenderAdapter.getElements(e);l.length>0&&(0,r.Z)("".concat(t,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then((function(){l.forEach((function(e){return s(void 0,void 0,void 0,(function(){var t,a,r;return c(this,(function(s){switch(s.label){case 0:if(e.parentElement.classList.contains("vditor-wysiwyg__pre")||e.parentElement.classList.contains("vditor-ir__marker--pre"))return[2];if(!(t=i.chartRenderAdapter.getCode(e).trim()))return[2];s.label=1;case 1:return s.trys.push([1,3,,4]),"true"===e.getAttribute("data-processed")?[2]:[4,(0,o.kY)(t)];case 2:return a=s.sent(),echarts.init(e,"dark"===n?"dark":void 0).setOption(a),e.setAttribute("data-processed","true"),[3,4];case 3:return r=s.sent(),e.className="vditor-reset--error",e.innerHTML="echarts render error: <br>".concat(r),[3,4];case 4:return[2]}}))}))}))}))}},51:(e,t,n)=>{n.d(t,{o:()=>i});var a=n(695),r=n(913),i=function(e,t){Array.from(e.querySelectorAll("pre > code")).filter((function(t,n){return!t.parentElement.classList.contains("vditor-wysiwyg__pre")&&!t.parentElement.classList.contains("vditor-ir__marker--pre")&&(!(t.classList.contains("language-mermaid")||t.classList.contains("language-flowchart")||t.classList.contains("language-echarts")||t.classList.contains("language-mindmap")||t.classList.contains("language-plantuml")||t.classList.contains("language-markmap")||t.classList.contains("language-abc")||t.classList.contains("language-graphviz")||t.classList.contains("language-math")||t.classList.contains("language-smiles"))&&(!(t.style.maxHeight.indexOf("px")>-1)&&!(e.classList.contains("vditor-preview")&&n>5)))})).forEach((function(e){var n,i,o,s=e.innerText;if(e.classList.contains("highlight-chroma")){var c=e.cloneNode(!0);c.querySelectorAll(".highlight-ln").forEach((function(e){e.remove()})),s=c.innerText}else s.endsWith("\n")&&(s=s.substr(0,s.length-1));var l='<svg><use xlink:href="#vditor-icon-copy"></use></svg>';document.getElementById("vditorIconScript")||(l='<svg viewBox="0 0 32 32"><path d="M22.545-0h-17.455c-1.6 0-2.909 1.309-2.909 2.909v20.364h2.909v-20.364h17.455v-2.909zM26.909 5.818h-16c-1.6 0-2.909 1.309-2.909 2.909v20.364c0 1.6 1.309 2.909 2.909 2.909h16c1.6 0 2.909-1.309 2.909-2.909v-20.364c0-1.6-1.309-2.909-2.909-2.909zM26.909 29.091h-16v-20.364h16v20.364z"></path></svg>');var d=document.createElement("div");d.className="vditor-copy",d.innerHTML='<span aria-label="'.concat((null===(n=window.VditorI18n)||void 0===n?void 0:n.copy)||"复制","\"\nonmouseover=\"this.setAttribute('aria-label', '").concat((null===(i=window.VditorI18n)||void 0===i?void 0:i.copy)||"复制","')\"\nclass=\"vditor-tooltipped vditor-tooltipped__w\"\nonclick=\"this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '").concat((null===(o=window.VditorI18n)||void 0===o?void 0:o.copied)||"已复制","');this.previousElementSibling.blur()\">").concat(l,"</span>");var u=document.createElement("textarea");u.value=(0,a.p)(s),d.insertAdjacentElement("afterbegin",u),t&&t.renderMenu&&t.renderMenu(e,d),e.before(d),e.style.maxHeight=window.outerHeight-40+"px",e.insertAdjacentHTML("afterend",'<span style="position: absolute">'.concat(r.Y.ZWSP,"</span>"))}))}},500:(e,t,n)=>{n.d(t,{D:()=>o});var a=n(913),r=n(161),i=n(59),o=function(e,t){void 0===t&&(t=a.Y.CDN);var n=i.flowchartRenderAdapter.getElements(e);0!==n.length&&(0,r.Z)("".concat(t,"/dist/js/flowchart.js/flowchart.min.js"),"vditorFlowchartScript").then((function(){n.forEach((function(e){if("true"!==e.getAttribute("data-processed")){var t=flowchart.parse(i.flowchartRenderAdapter.getCode(e));e.innerHTML="",t.drawSVG(e),e.setAttribute("data-processed","true")}}))}))}},339:(e,t,n)=>{n.d(t,{m:()=>o});var a=n(913),r=n(161),i=n(59),o=function(e,t){void 0===t&&(t=a.Y.CDN);var n=i.graphvizRenderAdapter.getElements(e);0!==n.length&&(0,r.Z)("".concat(t,"/dist/js/graphviz/viz.js"),"vditorGraphVizScript").then((function(){n.forEach((function(e){var t=i.graphvizRenderAdapter.getCode(e);if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")&&"true"!==e.getAttribute("data-processed")&&""!==t.trim()){try{var n=new Blob(["importScripts('".concat(document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js"),"');")],{type:"application/javascript"}),a=(window.URL||window.webkitURL).createObjectURL(n),r=new Worker(a);new Viz({worker:r}).renderSVGElement(t).then((function(t){e.innerHTML=t.outerHTML})).catch((function(t){e.innerHTML="graphviz render error: <br>".concat(t),e.className="vditor-reset--error"}))}catch(e){console.error("graphviz error",e)}e.setAttribute("data-processed","true")}}))}))}},108:(e,t,n)=>{n.d(t,{$:()=>o});var a=n(913),r=n(161),i=n(505),o=function(e,t,n){void 0===t&&(t=document),void 0===n&&(n=a.Y.CDN);var o=e.style;a.Y.CODE_THEME.includes(o)||(o="github");var s=document.getElementById("vditorHljsStyle"),c="".concat(n,"/dist/js/highlight.js/styles/").concat(o,".min.css");(s&&s.getAttribute("href")!==c&&s.remove(),(0,i.T)("".concat(n,"/dist/js/highlight.js/styles/").concat(o,".min.css"),"vditorHljsStyle"),!1!==e.enable)&&(0!==t.querySelectorAll("pre > code").length&&(0,r.Z)("".concat(n,"/dist/js/highlight.js/highlight.min.js?v=11.7.0"),"vditorHljsScript").then((function(){(0,r.Z)("".concat(n,"/dist/js/highlight.js/third-languages.js?v=1.0.1"),"vditorHljsThirdScript").then((function(){t.querySelectorAll("pre > code").forEach((function(t){if(!t.parentElement.classList.contains("vditor-ir__marker--pre")&&!t.parentElement.classList.contains("vditor-wysiwyg__pre")&&!(t.classList.contains("language-mermaid")||t.classList.contains("language-flowchart")||t.classList.contains("language-echarts")||t.classList.contains("language-mindmap")||t.classList.contains("language-plantuml")||t.classList.contains("language-smiles")||t.classList.contains("language-abc")||t.classList.contains("language-graphviz")||t.classList.contains("language-math"))){""!==e.defaultLang&&-1===t.className.indexOf("language-")&&t.classList.add("language-"+e.defaultLang);var n=t.className.replace("language-","");if(window.hljs.getLanguage(n)||(n="plaintext"),t.innerHTML=window.hljs.highlight(t.textContent,{language:n,ignoreIllegals:!0}).value,t.classList.add("hljs"),e.lineNumber){t.classList.add("vditor-linenumber");var a=t.querySelector(".vditor-linenumber__temp");a||((a=document.createElement("div")).className="vditor-linenumber__temp",t.insertAdjacentElement("beforeend",a));var r=getComputedStyle(t).whiteSpace,i=!1;"pre-wrap"!==r&&"pre-line"!==r||(i=!0);var o="",s=t.textContent.split(/\r\n|\r|\n/g);s.pop(),s.map((function(e){var t="";i&&(a.textContent=e||"\n",t=' style="height:'.concat(a.getBoundingClientRect().height,'px"')),o+="<span".concat(t,"></span>")})),a.style.display="none",o='<span class="vditor-linenumber__rows">'.concat(o,"</span>"),t.insertAdjacentHTML("beforeend",o)}}}))}))})))}},597:(e,t,n)=>{n.d(t,{K:()=>c});var a=n(913),r=n(161),i=n(59),o={},s=function(e,t){var n=window.markmap,a=n.Transformer,r=n.Markmap,i=n.deriveOptions,s=(n.globalCSS,new a);e.innerHTML='<svg style="width:100%"></svg>';var c=e.firstChild,l=r.create(c,null),d=function(e,t){var n=e.transform(t),a=Object.keys(n.features).filter((function(e){return!o[e]}));a.forEach((function(e){o[e]=!0}));var r=e.getAssets(a),i=r.styles,s=r.scripts,c=window.markmap;return i&&c.loadCSS(i),s&&c.loadJS(s),n}(s,t),u=d.root,m=d.frontmatter,h=i(null==m?void 0:m.markmap);l.setData(u,h),l.fit()},c=function(e,t){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var n=i.markmapRenderAdapter.getElements(e);0!==n.length&&(0,r.Z)("".concat(t,"/dist/js/markmap/markmap.min.js"),"vditorMarkerScript").then((function(){n.forEach((function(e){var t=i.markmapRenderAdapter.getCode(e);if("true"!==e.getAttribute("data-processed")&&""!==t.trim()){var n=document.createElement("div");n.className="language-markmap",e.parentNode.appendChild(n),s(n,t),"CODE"==e.parentNode.childNodes[0].nodeName&&e.parentNode.removeChild(e.parentNode.childNodes[0])}}))}))}},960:(e,t,n)=>{n.d(t,{T:()=>c});var a=n(913),r=n(161),i=n(505),o=n(695),s=n(59),c=function(e,t){void 0===e&&(e=document);var n=s.mathRenderAdapter.getElements(e);if(0!==n.length){var c={cdn:a.Y.CDN,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};if(t&&t.math&&(t.math=Object.assign({},c.math,t.math)),"KaTeX"===(t=Object.assign({},c,t)).math.engine)(0,i.T)("".concat(t.cdn,"/dist/js/katex/katex.min.css?v=0.16.9"),"vditorKatexStyle"),(0,r.Z)("".concat(t.cdn,"/dist/js/katex/katex.min.js?v=0.16.9"),"vditorKatexScript").then((function(){(0,r.Z)("".concat(t.cdn,"/dist/js/katex/mhchem.min.js?v=0.16.9"),"vditorKatexChemScript").then((function(){n.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")&&!e.getAttribute("data-math")){var n=(0,o.p)(s.mathRenderAdapter.getCode(e));e.setAttribute("data-math",n);try{e.innerHTML=katex.renderToString(n,{displayMode:"DIV"===e.tagName,output:"html",macros:t.math.macros})}catch(t){e.innerHTML=t.message,e.className="language-math vditor-reset--error"}e.addEventListener("copy",(function(e){e.stopPropagation(),e.preventDefault();var t=e.currentTarget.closest(".language-math");e.clipboardData.setData("text/html",t.innerHTML),e.clipboardData.setData("text/plain",t.getAttribute("data-math"))}))}}))}))}));else if("MathJax"===t.math.engine){window.MathJax||(window.MathJax={loader:{paths:{mathjax:"".concat(t.cdn,"/dist/js/mathjax")}},startup:{typeset:!1},tex:{macros:t.math.macros}},Object.assign(window.MathJax,t.math.mathJaxOptions)),(0,r.U)("".concat(t.cdn,"/dist/js/mathjax/tex-svg-full.js"),"protyleMathJaxScript");var l=function(e,t){var n=(0,o.p)(e.textContent).trim(),a=window.MathJax.getMetricsFor(e);a.display="DIV"===e.tagName,window.MathJax.tex2svgPromise(n,a).then((function(a){e.innerHTML="",e.setAttribute("data-math",n),e.append(a),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var r=a.querySelector('[data-mml-node="merror"]');r&&""!==r.textContent.trim()&&(e.innerHTML=r.textContent.trim(),e.className="vditor-reset--error"),t&&t()}))};window.MathJax.startup.promise.then((function(){for(var e=[],t=function(t){var a=n[t];a.parentElement.classList.contains("vditor-wysiwyg__pre")||a.parentElement.classList.contains("vditor-ir__marker--pre")||a.getAttribute("data-math")||!(0,o.p)(a.textContent).trim()||e.push((function(e){t===n.length-1?l(a):l(a,e)}))},a=0;a<n.length;a++)t(a);!function(e){if(0!==e.length){var t=0,n=e[e.length-1],a=function(){var r=e[t++];r===n?r():r(a)};a()}}(e)}))}}}},0:(e,t,n)=>{n.d(t,{l:()=>r});var a=n(933),r=function(e){e&&e.querySelectorAll("a").forEach((function(e){var t=e.getAttribute("href");t&&(t.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<video controls="controls" src="'.concat(t,'"></video>')),e.remove()}(e,t):t.match(/^.+.(mp3|wav|flac)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<audio controls="controls" src="'.concat(t,'"></audio>')),e.remove()}(e,t):function(e,t){var n=t.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),r=t.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),i=t.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),o=t.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),s=t.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),c=t.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),l=t.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),d=t.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);if(n&&11===n[1].length)e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'.concat(n[1]+(n[2]?"?start="+n[2]:""),'"></iframe>')),e.remove();else if(r&&r[1])e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'.concat(r[1],'"></iframe>')),e.remove();else if(i&&i[1])e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='.concat(i[1],'"></iframe>')),e.remove();else if(o&&o[1])e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="//coub.com/embed/'.concat(o[1],'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>')),e.remove();else if(s&&s[0])e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.facebook.com/plugins/video.php?href='.concat(encodeURIComponent(s[0]),'"></iframe>')),e.remove();else if(c&&c[2])e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.dailymotion.com/embed/video/'.concat(c[2],'"></iframe>')),e.remove();else if(t.indexOf("bilibili.com")>-1&&(t.indexOf("bvid=")>-1||l&&l[1])){var u={bvid:(0,a.TK)("bvid",t)||l&&l[1],page:"1",high_quality:"1",as_wide:"1",allowfullscreen:"true",autoplay:"0"};new URL(t.startsWith("http")?t:"https:"+t).search.split("&").forEach((function(e,t){if(e){0===t&&(e=e.substr(1));var n=e.split("=");u[n[0]]=n[1]}}));var m="https://player.bilibili.com/player.html?",h=Object.keys(u);h.forEach((function(e,t){m+="".concat(e,"=").concat(u[e]),t<h.length-1&&(m+="&")})),e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="'.concat(m,'"></iframe>')),e.remove()}else d&&d[1]&&(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'.concat(d[1],'"></iframe>')),e.remove())}(e,t))}))}},975:(e,t,n)=>{n.d(t,{e:()=>l});var a=n(913),r=n(161),i=n(59),o=n(933),s=function(e,t,n,a){return new(n||(n=Promise))((function(r,i){function o(e){try{c(a.next(e))}catch(e){i(e)}}function s(e){try{c(a.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((a=a.apply(e,t||[])).next())}))},c=function(e,t){var n,a,r,i,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(o=0)),o;)try{if(n=1,a&&(r=2&s[0]?a.return:s[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,s[1])).done)return r;switch(a=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,a=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){o.label=s[1];break}if(6===s[0]&&o.label<r[1]){o.label=r[1],r=s;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(s);break}r[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],a=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},l=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var l=i.mermaidRenderAdapter.getElements(e);0!==l.length&&(0,r.Z)("".concat(t,"/dist/js/mermaid/mermaid.min.js"),"vditorMermaidScript").then((function(){var e={securityLevel:"loose",altFontFamily:"sans-serif",fontFamily:"sans-serif",startOnLoad:!1,flowchart:{htmlLabels:!0,useMaxWidth:!0},sequence:{useMaxWidth:!0,diagramMarginX:8,diagramMarginY:8,boxMargin:8,showSequenceNumbers:!0},gantt:{leftPadding:75,rightPadding:20}};"dark"===n&&(e.theme="dark"),mermaid.initialize(e),l.forEach((function(e){return s(void 0,void 0,void 0,(function(){var t,n,a,r,s;return c(this,(function(c){switch(c.label){case 0:if(t=i.mermaidRenderAdapter.getCode(e),"true"===e.getAttribute("data-processed")||""===t.trim())return[2];n="mermaid"+(0,o.Ee)(),c.label=1;case 1:return c.trys.push([1,3,,4]),[4,mermaid.render(n,e.textContent)];case 2:return a=c.sent(),e.innerHTML=a.svg,[3,4];case 3:return r=c.sent(),s=document.querySelector("#"+n),e.innerHTML="".concat(s.outerHTML,'<br>\n<div style="text-align: left"><small>').concat(r.message.replace(/\n/,"<br>"),"</small></div>"),s.parentElement.remove(),[3,4];case 4:return e.setAttribute("data-processed","true"),[2]}}))}))}))}))}},162:(e,t,n)=>{n.d(t,{l:()=>o});var a=n(913),r=n(161),i=n(59),o=function(e,t,n){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var o=i.mindmapRenderAdapter.getElements(e);o.length>0&&(0,r.Z)("".concat(t,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then((function(){o.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")){var t=i.mindmapRenderAdapter.getCode(e);if(t)try{if("true"===e.getAttribute("data-processed"))return;echarts.init(e,"dark"===n?"dark":void 0).setOption({series:[{data:[JSON.parse(decodeURIComponent(t))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(e,t){var n;return(null===(n=null==t?void 0:t.data)||void 0===n?void 0:n.children)?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}}),e.setAttribute("data-processed","true")}catch(t){e.className="vditor-reset--error",e.innerHTML="mindmap render error: <br>".concat(t)}}}))}))}},70:(e,t,n)=>{n.d(t,{N:()=>i});var a=n(164),r=n(960),i=function(e,t,n){var i="",o=[];if(Array.from(e.children).forEach((function(e,t){if((0,a.c)(e)){if(n){var r=e.id.lastIndexOf("_");e.id=e.id.substring(0,-1===r?void 0:r)+"_"+t}o.push(e.id),i+=e.outerHTML.replace("<wbr>","")}})),""===i)return t.innerHTML="","";var s=document.createElement("div");if(n)n.lute.SetToC(!0),"wysiwyg"!==n.currentMode||n.preview.element.contains(e)?"ir"!==n.currentMode||n.preview.element.contains(e)?s.innerHTML=n.lute.HTML2VditorDOM("<p>[ToC]</p>"+i):s.innerHTML=n.lute.SpinVditorIRDOM("<p>[ToC]</p>"+i):s.innerHTML=n.lute.SpinVditorDOM("<p>[ToC]</p>"+i),n.lute.SetToC(n.options.preview.markdown.toc);else{t.classList.add("vditor-outline");var c=Lute.New();c.SetToC(!0),s.innerHTML=c.HTML2VditorDOM("<p>[ToC]</p>"+i)}var l=s.firstElementChild.querySelectorAll("li > span[data-target-id]");return l.forEach((function(e,t){if(e.nextElementSibling&&"UL"===e.nextElementSibling.tagName){var n="<svg class='vditor-outline__action'><use xlink:href='#vditor-icon-down'></use></svg>";document.getElementById("vditorIconScript")||(n='<svg class="vditor-outline__action" viewBox="0 0 32 32"><path d="M3.76 6.12l12.24 12.213 12.24-12.213 3.76 3.76-16 16-16-16 3.76-3.76z"></path></svg>'),e.innerHTML="".concat(n,"<span>").concat(e.innerHTML,"</span>")}else e.innerHTML="<svg></svg><span>".concat(e.innerHTML,"</span>");e.setAttribute("data-target-id",o[t])})),i=s.firstElementChild.innerHTML,0===l.length?(t.innerHTML="",i):(t.innerHTML=i,n&&(0,r.T)(t,{cdn:n.options.cdn,math:n.options.preview.math}),t.firstElementChild.addEventListener("click",(function(a){for(var r=a.target;r&&!r.isEqualNode(t);){if(r.classList.contains("vditor-outline__action")){r.classList.contains("vditor-outline__action--close")?(r.classList.remove("vditor-outline__action--close"),r.parentElement.nextElementSibling.setAttribute("style","display:block")):(r.classList.add("vditor-outline__action--close"),r.parentElement.nextElementSibling.setAttribute("style","display:none")),a.preventDefault(),a.stopPropagation();break}if(r.getAttribute("data-target-id")){a.preventDefault(),a.stopPropagation();var i=document.getElementById(r.getAttribute("data-target-id"));if(!i)return;if(n)if("auto"===n.options.height){var o=i.offsetTop+n.element.offsetTop;n.options.toolbarConfig.pin||(o+=n.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,o)}else n.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,n.element.offsetTop),n.preview.element.contains(e)?e.parentElement.scrollTop=i.offsetTop:e.scrollTop=i.offsetTop;else window.scrollTo(window.scrollX,i.offsetTop);break}r=r.parentElement}})),i)}},591:(e,t,n)=>{n.d(t,{M:()=>o});var a=n(913),r=n(161),i=n(59),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t=a.Y.CDN);var n=i.plantumlRenderAdapter.getElements(e);0!==n.length&&(0,r.Z)("".concat(t,"/dist/js/plantuml/plantuml-encoder.min.js"),"vditorPlantumlScript").then((function(){n.forEach((function(e){if(!e.parentElement.classList.contains("vditor-wysiwyg__pre")&&!e.parentElement.classList.contains("vditor-ir__marker--pre")){var t=i.plantumlRenderAdapter.getCode(e).trim();if(t)try{e.innerHTML='<object type="image/svg+xml" data="https://www.plantuml.com/plantuml/svg/~1'.concat(plantumlEncoder.encode(t),'"/>')}catch(t){e.className="vditor-reset--error",e.innerHTML="plantuml render error: <br>".concat(t)}}}))}))}},796:(e,t,n)=>{n.d(t,{X:()=>a});var a=function(e){var t=Lute.New();return t.PutEmojis(e.emojis),t.SetEmojiSite(e.emojiSite),t.SetHeadingAnchor(e.headingAnchor),t.SetInlineMathAllowDigitAfterOpenMarker(e.inlineMathDigit),t.SetAutoSpace(e.autoSpace),t.SetToC(e.toc),t.SetFootnotes(e.footnotes),t.SetFixTermTypo(e.fixTermTypo),t.SetVditorCodeBlockPreview(e.codeBlockPreview),t.SetVditorMathBlockPreview(e.mathBlockPreview),t.SetSanitize(e.sanitize),t.SetChineseParagraphBeginningSpace(e.paragraphBeginningSpace),t.SetRenderListStyle(e.listStyle),t.SetLinkBase(e.linkBase),t.SetLinkPrefix(e.linkPrefix),t.SetMark(e.mark),t.SetGFMAutoLink(e.gfmAutoLink),e.lazyLoadImage&&t.SetImageLazyLoading(e.lazyLoadImage),t}},726:(e,t,n)=>{n.d(t,{o:()=>a});var a=function(e,t,n){void 0===t&&(t="zh_CN"),void 0===n&&(n="classic");var a=e.getBoundingClientRect();document.body.insertAdjacentHTML("beforeend",'<div class="vditor vditor-img'.concat("dark"===n?" vditor--dark":"",'">\n    <div class="vditor-img__bar">\n      <span class="vditor-img__btn" data-deg="0">\n        <svg><use xlink:href="#vditor-icon-redo"></use></svg>\n        ').concat(window.VditorI18n.spin,"\n      </span>\n      <span class=\"vditor-img__btn\"  onclick=\"this.parentElement.parentElement.outerHTML = '';document.body.style.overflow = ''\">\n        X &nbsp;").concat(window.VditorI18n.close,'\n      </span>\n    </div>\n    <div class="vditor-img__img" onclick="this.parentElement.outerHTML = \'\';document.body.style.overflow = \'\'">\n      <img style="width: ').concat(e.width,"px;height:").concat(e.height,"px;transform: translate3d(").concat(a.left,"px, ").concat(a.top-36,'px, 0)" src="').concat(e.getAttribute("src"),'">\n    </div>\n</div>')),document.body.style.overflow="hidden";var r=document.querySelector(".vditor-img img"),i="translate3d(".concat(Math.max(0,window.innerWidth-e.naturalWidth)/2,"px, ").concat(Math.max(0,window.innerHeight-36-e.naturalHeight)/2,"px, 0)");setTimeout((function(){r.setAttribute("style","transition: transform .3s ease-in-out;transform: ".concat(i)),setTimeout((function(){r.parentElement.scrollTo((r.parentElement.scrollWidth-r.parentElement.clientWidth)/2,(r.parentElement.scrollHeight-r.parentElement.clientHeight)/2)}),400)}));var o=document.querySelector(".vditor-img__btn");o.addEventListener("click",(function(){var t=parseInt(o.getAttribute("data-deg"),10)+90;t/90%2==1&&e.naturalWidth>r.parentElement.clientHeight?r.style.transform="translate3d(".concat(Math.max(0,window.innerWidth-e.naturalWidth)/2,"px, ").concat(e.naturalWidth/2-e.naturalHeight/2,"px, 0) rotateZ(").concat(t,"deg)"):r.style.transform="".concat(i," rotateZ(").concat(t,"deg)"),o.setAttribute("data-deg",t.toString()),setTimeout((function(){r.parentElement.scrollTo((r.parentElement.scrollWidth-r.parentElement.clientWidth)/2,(r.parentElement.scrollHeight-r.parentElement.clientHeight)/2)}),400)}))}},13:(e,t,n)=>{n.d(t,{h:()=>i});var a=n(913),r=n(505),i=function(e,t){void 0===t&&(t=a.Y.CDN),a.Y.CODE_THEME.includes(e)||(e="github");var n=document.getElementById("vditorHljsStyle"),i="".concat(t,"/dist/js/highlight.js/styles/").concat(e,".min.css");n?n.getAttribute("href")!==i&&(n.remove(),(0,r.T)(i,"vditorHljsStyle")):(0,r.T)(i,"vditorHljsStyle")}},873:(e,t,n)=>{n.d(t,{H:()=>r});var a=n(505),r=function(e,t){if(e&&t){var n=document.getElementById("vditorContentTheme"),r="".concat(t,"/").concat(e,".css");n?n.getAttribute("href")!==r&&(n.remove(),(0,a.T)(r,"vditorContentTheme")):(0,a.T)(r,"vditorContentTheme")}}},161:(e,t,n)=>{n.d(t,{U:()=>a,Z:()=>r});var a=function(e,t){if(document.getElementById(t))return!1;var n=new XMLHttpRequest;n.open("GET",e,!1),n.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),n.send("");var a=document.createElement("script");a.type="text/javascript",a.text=n.responseText,a.id=t,document.head.appendChild(a)},r=function(e,t){return new Promise((function(n,a){if(document.getElementById(t))return n(!0),!1;var r=document.createElement("script");r.src=e,r.async=!0,document.head.appendChild(r),r.onerror=function(e){a(e)},r.onload=function(){if(document.getElementById(t))return r.remove(),n(!0),!1;r.id=t,n(!0)}}))}},505:(e,t,n)=>{n.d(t,{T:()=>a});var a=function(e,t){if(!document.getElementById(t)){var n=document.createElement("link");n.id=t,n.rel="stylesheet",n.type="text/css",n.href=e,document.getElementsByTagName("head")[0].appendChild(n)}}},695:(e,t,n)=>{n.d(t,{p:()=>a});var a=function(e){return e.replace(/\u00a0/g," ")}},933:(e,t,n)=>{n.d(t,{Ee:()=>a,TK:()=>r,kY:()=>i});var a=function(){return([1e7].toString()+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(function(e){return(parseInt(e,10)^window.crypto.getRandomValues(new Uint32Array(1))[0]&15>>parseInt(e,10)/4).toString(16)}))},r=function(e,t){void 0===t&&(t=window.location.search);var n=t.substring(t.indexOf("?")),a=n.indexOf("#");return new URLSearchParams(n.substring(0,a>=0?a:void 0)).get(e)},i=function(e){return Function('"use strict";return ('.concat(e,")"))()}},598:(e,t,n)=>{n.d(t,{KJ:()=>r,_Y:()=>a});var a=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,a=!1;n&&!a&&!n.classList.contains("vditor-reset");)n.nodeName===t?a=!0:n=n.parentElement;return a&&n},r=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,a=!1;n&&!a&&!n.classList.contains("vditor-reset");)n.classList.contains(t)?a=!0:n=n.parentElement;return a&&n}},164:(e,t,n)=>{n.d(t,{c:()=>a});var a=function(e){var t=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,a=!1;n&&!a&&!n.classList.contains("vditor-reset");)0===n.nodeName.indexOf(t)?a=!0:n=n.parentElement;return a&&n}(e,"H");return!(!t||2!==t.tagName.length||"HR"===t.tagName)&&t}},905:(e,t,n)=>{n.d(t,{h:()=>a});var a=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n={},r=function(e){for(var t in e)e.hasOwnProperty(t)&&("[object Object]"===Object.prototype.toString.call(e[t])?n[t]=a(n[t],e[t]):n[t]=e[t])},i=0;i<e.length;i++)r(e[i]);return n}},827:(e,t,n)=>{n.d(t,{jl:()=>a});n(913);var a=function(e){var t=window.getSelection();t.removeAllRanges(),t.addRange(e)}}},t={};function n(a){var r=t[a];if(void 0!==r)return r.exports;var i=t[a]={exports:{}};return e[a](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};n.d(a,{default:()=>N});var r=n(288),i=n(59),o=n(784),s=n(51),c=n(500),l=n(339),d=n(108),u=function(e){void 0===e&&(e=document);var t=function(e){var t=document.createElement("img");t.src=e.getAttribute("data-src"),t.addEventListener("load",(function(){e.getAttribute("style")||e.getAttribute("class")||e.getAttribute("width")||e.getAttribute("height")||t.naturalHeight>t.naturalWidth&&t.naturalWidth/t.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&t.naturalHeight>window.innerHeight-40&&(e.style.height=window.innerHeight-40+"px"),e.src=t.src})),e.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return e.querySelectorAll("img").forEach((function(e){e.getAttribute("data-src")&&t(e)})),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)}))):(window.vditorImageIntersectionObserver=new IntersectionObserver((function(e){e.forEach((function(e){(void 0===e.isIntersecting?0!==e.intersectionRatio:e.isIntersecting)&&e.target.getAttribute("data-src")&&t(e.target)}))})),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)})))},m=n(960),h=n(0),p=n(975),g=n(931),v=n(597),f=n(162),b=n(70),w=n(591),y=n(913),k=n(873),S=n(161),E=n(598),L=n(905),T=n(796),x=n(827),A=function(e,t){if(void 0===t&&(t="zh_CN"),"undefined"!=typeof speechSynthesis&&"undefined"!=typeof SpeechSynthesisUtterance){var n=function(){var e,n;return speechSynthesis.getVoices().forEach((function(a){a.lang===t.replace("_","-")&&(e=a),a.default&&(n=a)})),e||(e=n),e},a='<svg><use xlink:href="#vditor-icon-play"></use></svg>',r='<svg><use xlink:href="#vditor-icon-pause"></use></svg>';document.getElementById("vditorIconScript")||(a='<svg viewBox="0 0 32 32"><path d="M3.436 0l25.128 16-25.128 16v-32z"></path></svg>',r='<svg viewBox="0 0 32 32"><path d="M20.617 0h9.128v32h-9.128v-32zM2.255 32v-32h9.128v32h-9.128z"></path></svg>');var i=document.querySelector(".vditor-speech");i||((i=document.createElement("button")).className="vditor-speech",e.insertAdjacentElement("beforeend",i),void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=n));var o=n(),s=new SpeechSynthesisUtterance;s.voice=o,s.onend=s.onerror=function(){i.style.display="none",speechSynthesis.cancel(),i.classList.remove("vditor-speech--current"),i.innerHTML=a},e.addEventListener(void 0!==window.ontouchstart?"touchend":"click",(function(t){var n=t.target;if(n.classList.contains("vditor-speech")||n.parentElement.classList.contains("vditor-speech"))return i.classList.contains("vditor-speech--current")?speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),i.innerHTML=r):(speechSynthesis.pause(),i.innerHTML=a)):(s.text=i.getAttribute("data-text"),speechSynthesis.speak(s),i.classList.add("vditor-speech--current"),i.innerHTML=r),(0,x.jl)(window.vditorSpeechRange),void e.focus();if(i.style.display="none",speechSynthesis.cancel(),i.classList.remove("vditor-speech--current"),i.innerHTML=a,0!==getSelection().rangeCount){var o=getSelection().getRangeAt(0),c=o.toString().trim();if(c){window.vditorSpeechRange=o.cloneRange();var l=o.getBoundingClientRect();i.innerHTML=a,i.style.display="block",i.style.top=l.top+l.height+document.querySelector("html").scrollTop-20+"px",void 0!==window.ontouchstart?i.style.left=t.changedTouches[t.changedTouches.length-1].pageX+2+"px":i.style.left=t.clientX+2+"px",i.setAttribute("data-text",c)}}}))}},M=function(e,t,n,a){return new(n||(n=Promise))((function(r,i){function o(e){try{c(a.next(e))}catch(e){i(e)}}function s(e){try{c(a.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,s)}c((a=a.apply(e,t||[])).next())}))},j=function(e,t){var n,a,r,i,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(o=0)),o;)try{if(n=1,a&&(r=2&s[0]?a.return:s[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,s[1])).done)return r;switch(a=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,a=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){o.label=s[1];break}if(6===s[0]&&o.label<r[1]){o.label=r[1],r=s;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(s);break}r[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],a=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},_=function(e){var t,n={anchor:0,cdn:y.Y.CDN,customEmoji:{},emojiPath:"".concat(y.Y.CDN,"/dist/images/emoji"),hljs:y.Y.HLJS_OPTIONS,icon:"ant",lang:"zh_CN",markdown:y.Y.MARKDOWN_OPTIONS,math:y.Y.MATH_OPTIONS,mode:"light",speech:{enable:!1},render:{media:{enable:!0}},theme:y.Y.THEME_OPTIONS};return e.cdn&&((null===(t=e.theme)||void 0===t?void 0:t.path)||(n.theme.path="".concat(e.cdn,"/dist/css/content-theme")),e.emojiPath||(n.emojiPath="".concat(e.cdn,"/dist/images/emoji"))),(0,L.h)(n,e)},H=function(e,t){var n=_(t);return(0,S.Z)("".concat(n.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then((function(){var a=(0,T.X)({autoSpace:n.markdown.autoSpace,gfmAutoLink:n.markdown.gfmAutoLink,codeBlockPreview:n.markdown.codeBlockPreview,emojiSite:n.emojiPath,emojis:n.customEmoji,fixTermTypo:n.markdown.fixTermTypo,footnotes:n.markdown.footnotes,headingAnchor:0!==n.anchor,inlineMathDigit:n.math.inlineDigit,lazyLoadImage:n.lazyLoadImage,linkBase:n.markdown.linkBase,linkPrefix:n.markdown.linkPrefix,listStyle:n.markdown.listStyle,mark:n.markdown.mark,mathBlockPreview:n.markdown.mathBlockPreview,paragraphBeginningSpace:n.markdown.paragraphBeginningSpace,sanitize:n.markdown.sanitize,toc:n.markdown.toc});return(null==t?void 0:t.renderers)&&a.SetJSRenderers({renderers:{Md2HTML:t.renderers}}),a.SetHeadingID(!0),a.Md2HTML(e)}))},C=function(e,t,n){return M(void 0,void 0,void 0,(function(){var a,i,b,y;return j(this,(function(L){switch(L.label){case 0:return a=_(n),[4,H(t,a)];case 1:if(i=L.sent(),a.transform&&(i=a.transform(i)),e.innerHTML=i,e.classList.add("vditor-reset"),a.i18n)return[3,5];if(["en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(a.lang))return[3,2];throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");case 2:return y=(b="vditorI18nScript")+a.lang,document.querySelectorAll('head script[id^="'.concat(b,'"]')).forEach((function(e){e.id!==y&&document.head.removeChild(e)})),[4,(0,S.Z)("".concat(a.cdn,"/dist/js/i18n/").concat(a.lang,".js"),y)];case 3:L.sent(),L.label=4;case 4:return[3,6];case 5:window.VditorI18n=a.i18n,L.label=6;case 6:return a.icon?[4,(0,S.Z)("".concat(a.cdn,"/dist/js/icons/").concat(a.icon,".js"),"vditorIconScript")]:[3,8];case 7:L.sent(),L.label=8;case 8:return(0,k.H)(a.theme.current,a.theme.path),1===a.anchor&&e.classList.add("vditor-reset--anchor"),(0,s.o)(e,a.hljs),(0,d.$)(a.hljs,e,a.cdn),(0,m.T)(e,{cdn:a.cdn,math:a.math}),(0,p.e)(e,a.cdn,a.mode),(0,g.Y)(e,a.cdn,a.mode),(0,v.K)(e,a.cdn),(0,c.D)(e,a.cdn),(0,l.m)(e,a.cdn),(0,o.v)(e,a.cdn,a.mode),(0,f.l)(e,a.cdn,a.mode),(0,w.M)(e,a.cdn),(0,r.$)(e,a.cdn),a.render.media.enable&&(0,h.l)(e),a.speech.enable&&A(e),0!==a.anchor&&(T=a.anchor,document.querySelectorAll(".vditor-anchor").forEach((function(e){1===T&&e.classList.add("vditor-anchor--left"),e.onclick=function(){var t=e.getAttribute("href").substr(1),n=document.getElementById("vditorAnchor-"+t).offsetTop;document.querySelector("html").scrollTop=n}})),window.onhashchange=function(){var e=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));e&&(document.querySelector("html").scrollTop=e.offsetTop)}),a.after&&a.after(),a.lazyLoadImage&&u(e),e.addEventListener("click",(function(t){var n=(0,E._Y)(t.target,"SPAN");if(n&&(0,E.KJ)(n,"vditor-toc")){var a=e.querySelector("#"+n.getAttribute("data-target-id"));a&&window.scrollTo(window.scrollX,a.offsetTop)}else;})),[2]}var T}))}))},R=n(726),I=n(13);const N=function(){function e(){}return e.adapterRender=i,e.previewImage=R.o,e.codeRender=s.o,e.graphvizRender=l.m,e.highlightRender=d.$,e.mathRender=m.T,e.mermaidRender=p.e,e.SMILESRender=g.Y,e.markmapRender=v.K,e.flowchartRender=c.D,e.chartRender=o.v,e.abcRender=r.$,e.mindmapRender=f.l,e.plantumlRender=w.M,e.outlineRender=b.N,e.mediaRender=h.l,e.speechRender=A,e.lazyLoadImageRender=u,e.md2html=H,e.preview=C,e.setCodeTheme=I.h,e.setContentTheme=k.H,e}();return a=a.default})()));