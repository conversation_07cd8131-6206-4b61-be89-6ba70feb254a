import { Outlet } from 'umi';
import React, { useState,useEffect } from 'react';
import styles from './index.less';
import { dispatchInUtils } from '@/utils';

const Layout = () => {
  /*
  useEffect(() => {
    dispatchInUtils({
      type: 'recentAiRead/fetchList',
      payload: { },
    });

    return () => {

    };
  }, []);
  */

  return (
    <main className={styles.mainContent}>
      <Outlet />
    </main>
  );
};
export default Layout;
