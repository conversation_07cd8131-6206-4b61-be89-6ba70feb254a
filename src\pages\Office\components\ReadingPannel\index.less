.chatWidthDocWrap {
  padding: 12px 0;
  max-width: 800px;
  height: 100%;
  margin: 0 auto;
  overflow-y: scroll;
  // position: absolute;
  // top: 50px;
  // bottom: 195px;
  // left: 0;
  // right: 0;
  // overflow: hidden;
  // display: flex;
  // flex-direction: column;


  .chatWidthDocRecentT1 {
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: normal;
    color: #3D3D3D;
    padding-bottom: 10px;
    display: flex;
    flex-direction: row;

    .chatWidthDocRecentT1in {
      flex: 1;
    }

    .chatWidthDocRecentT1inicon {
      cursor: pointer;
    }
  }

  .chatWidtDocItem {
    background: #F3F4F6;
  }

  .chatWidtDocItemCover {
    height: 120px;
    background: #F3F4F6;
    overflow: hidden;
    box-sizing: border-box;
    padding-top: 10px;

    img {
      width: 158px;
      display: block;
      margin: 0 auto;
      border-radius: 6px;
      box-shadow: 0 -2px 10px 0 rgba(205, 205, 205, 30%);
    }

    .chatWidtDocItemCoverIns {
      display: flex;
      flex-direction: row;
      position: relative;
    }

    .chatWidtDocItemCoverInsIcon {
      position: absolute;
      transition: transform .2s ease-in-out;
    }

    .chatWidtDocItemCoverIns_2 {
      .chatWidtDocItemCoverInsIcon_0 {
        left: 10%;
      }

      .chatWidtDocItemCoverInsIcon_1 {
        right: 10%;
      }

    }

    .chatWidtDocItemCoverIns_2:hover {
      .chatWidtDocItemCoverInsIcon_0 {
        transform: rotate(-5deg);
      }

      .chatWidtDocItemCoverInsIcon_1 {
        transform: rotate(5deg);
      }

    }

    .chatWidtDocItemCoverIns_3 {
      .chatWidtDocItemCoverInsIcon_0 {
        left: 10%;
      }

      .chatWidtDocItemCoverInsIcon_1 {
        right: 20%;
      }

      .chatWidtDocItemCoverInsIcon_2 {
        right: 10%;
      }

    }

    .chatWidtDocItemCoverIns_3:hover {
      .chatWidtDocItemCoverInsIcon_0 {
        transform: rotate(-5deg);
      }

      .chatWidtDocItemCoverInsIcon_1 {
        transform: rotate(-2deg);
      }

      .chatWidtDocItemCoverInsIcon_2 {
        transform: rotate(5deg);
      }

    }

    .chatWidtDocItemCoverIns_4 {
      .chatWidtDocItemCoverInsIcon_0 {
        left: 10%;
      }

      .chatWidtDocItemCoverInsIcon_1 {
        right: 30%;
      }

      .chatWidtDocItemCoverInsIcon_2 {
        right: 20%;
      }

      .chatWidtDocItemCoverInsIcon_3 {
        right: 10%;
      }

    }

    .chatWidtDocItemCoverIns_4:hover {
      .chatWidtDocItemCoverInsIcon_0 {
        transform: rotate(-5deg);
      }

      .chatWidtDocItemCoverInsIcon_1 {
        transform: rotate(-2deg);
      }

      .chatWidtDocItemCoverInsIcon_2 {
        transform: rotate(-1deg);
      }

      .chatWidtDocItemCoverInsIcon_3 {
        transform: rotate(5deg);
      }

    }

    .chatWidtDocItemCoverIns_5 {
      .chatWidtDocItemCoverInsIcon_0 {
        left: 10%;
      }

      .chatWidtDocItemCoverInsIcon_1 {
        right: 25%;
      }

      .chatWidtDocItemCoverInsIcon_2 {
        right: 20%;
      }

      .chatWidtDocItemCoverInsIcon_3 {
        right: 15%;
      }

      .chatWidtDocItemCoverInsIcon_4 {
        right: 10%;
      }

    }

    .chatWidtDocItemCoverIns_5:hover {
      .chatWidtDocItemCoverInsIcon_0 {
        transform: rotate(-5deg);
      }

      .chatWidtDocItemCoverInsIcon_1 {
        transform: rotate(-2deg);
      }

      .chatWidtDocItemCoverInsIcon_2 {
        transform: rotate(-1deg);
      }

      .chatWidtDocItemCoverInsIcon_3 {
        transform: rotate(0);
      }

      .chatWidtDocItemCoverInsIcon_4 {
        transform: rotate(5deg);
      }

    }



  }

  .chatDocThumbWrap {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .chatDocListWrap {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;

    .chatWidtDocItemDesc {
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
  }

  .chatWidtDocItemDesc {
    border-radius: 8px;
    background: #FFF;
    margin: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px;

    .chatWidtDocItemDescDiv {
      width: 25px;
      position: relative;
    }

    .chatWidtDocItemDescNum {
      position: absolute;
      right: -2px;
      bottom: -2px;
      font-size: 9px;
      color: #333;
      background-color: #fff;
      padding: 0 2px;
      border-radius: 20%;
    }

    .chatWidtDocItemDescIcon {
      width: 25px;
      max-width: none;
      height: 26px;
    }

    .chatWidtDocItemDescIn {
      flex: 1;
      padding-left: 10px;
      overflow: hidden;
    }

    .chatWidtDocItemDescTit {
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: normal;
      color: #000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }


    .chatWidtDocItemDescInf {
      font-family: "PingFang SC";
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #999;
    }
  }
}

.chatWidthDocWrap::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.chatNoRead {
  font-size: 14px;
  color: #333;
  text-align: center;
  padding: 20px 0;
}
