.pdfContainerWrap {
	display: flex;
	position: relative;
	overflow: hidden;
	height: 100%;
	flex-direction: column;
	box-shadow: 0 4px 10px 4px rgba(158, 158, 158, 20%);

	.pdfContainerDoc {
		display:flex;
		flex:1;
		overflow:hidden;
	}


	.pdfContainerToolsBar {
		height: 53px;
		border-bottom:1px solid #eee;
		display: flex;
		flex-direction: row;
		align-items: center;
		box-sizing:border-box;
		padding:0 10px;
    position: relative;
    z-index: 100;

		.btnThumbControl {
			padding-right:10px;
			box-sizing:border-box;
			cursor: pointer;

			img {
				width:24px;
				height:auto;
			}
		}


		.transBtn {
			display: flex;
			flex-direction: row;
			align-items: center;
			padding:5px 12px;
			border-radius: 8px;
			cursor:pointer;

			&:hover {
				background: #EFEFEF;
			}

      .transInBtn {
      	display: flex;
      	flex-direction: row;
      	align-items: center;

        img {
        	width:23px;
        	height:auto;
        	margin-right:3px;
        }
      }


		}

		.langSelectorBtn {
			cursor: pointer;
			height: 23px;
			width: 23px;
			border-radius:3px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			margin-left:5px;

			&:hover {
				background: #ccc;
			}
		}

		.title {
			flex:1;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-family: "PingFang SC";
			font-size: 16px;
			font-weight: 600;
			line-height: 24px;
			letter-spacing: normal;
			color: #000;
		}


		.btnToBig {
			padding-left:10px;
			box-sizing:border-box;
			cursor: pointer;

			img {
				width:24px;
				height:auto;
			}
		}

		.btnClose {
			padding-left:10px;
			box-sizing:border-box;
			cursor: pointer;

			img {
				width:24px;
				height:auto;
			}
		}

		.flex1 {
			flex:1;
		}
	}

  .pdfContainerW {
    width: 100vh;
  }

	.pdfContainerIn {
		flex:1;
		overflow-x:hidden;
		overflow-y:auto;
		background: #F6F6F6;
		display: flex;
		flex-direction: row;
		justify-content: center;

		.pdfContainerInX {
			display: flex;
			flex-direction: column;
		}

		.pdfContainerInX1 {
			margin-left:10px;
		}
	}


	.pdfContainerSelectBox {
		position: absolute ;
		display: none ;
		opacity: 0.2;
		background: #005BF8;

		&:hover {
			background:#fff;
		}
	}

	.pdfContainerSelectTransBox {
		cursor:pointer;
		display: block;
    background:#fff;
    opacity: 0;
    //opacity: 1;
    overflow: hidden;

    &:hover{
      padding: 5px;
      overflow: visible;
      background: #ededed;
      z-index: 100;
      right: 5%;
      // width: auto !important;
      height: auto !important;
    }

	}

  .pdfContainerSelectTransedBox {
    background:#fff;
    opacity: 1;
    font-size: 80%;

    &::after {
        background-image: linear-gradient(180deg, transparent, #fff);
        bottom: 0;
        content: "";
        height: 20px;
        left: 0;
        max-height: calc(100% - 8px);
        position: absolute;
        right: 0;
    }

    &:hover::after {
      display: none;
    }
  }


  .pdfContainerSelectToolsBar {
    border-radius: 16px;
    background: #FFF;
    box-sizing: border-box;
    border: 1px solid #EBEEF2;
    box-shadow: 0 4px 8px 0 rgba(222, 229, 241, 57%);
    display: flex;
    flex-direction: row;
    align-items : center;
    position: absolute;
    font-size:12px;
    padding:4px 3px;
    z-index:5;

    .transBtn {
      margin:0 5px;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding:3px 5px ;
      border-radius: 5px;
      overflow: hidden;

      .transTxtBtn {
        cursor: pointer;
      }

      .toTransBtn {
          padding:0 5px;
          border-radius: 5px;
          margin-left: 5px;

          &:hover {
            background: #ccc;
          }
      }

      &:hover{
        background: #eee;
      }

      .icon {
        margin-right:5px;
      }
    }

    .copyBtn {
       margin:0 5px;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      padding:3px 5px ;
      border-radius: 5px;
      overflow: hidden;

      &:hover{
        background: #eee;
      }

      .icon {
        margin-right:5px;
      }
    }

    .qBtn {
      margin:0 5px;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      padding: 3px 5px ;
      border-radius: 5px;
      overflow: hidden;

      &:hover{
        background: #eee;
      }


      .icon {
        margin-right:5px;
      }
    }
  }


}


.langSelectorWrap {
	padding:10px;
	border-radius: 5px;
	background: #FFF;
	box-sizing: border-box;
	border: 1px solid #EFEFEF;
	box-shadow: 1px 5px 9px 0 rgba(187, 187, 187, 50%);
	width: 232px;

	.langSelectorTitle {
		display: flex;
		flex-direction: row;
		align-items: center;
		border-bottom: 1px solid #EBEEF2;
		padding-bottom:10px;

		.flex1 {
			flex:1;
		}
	}

	.langSelectorItem {
		padding:5px 0 ;

		.title {
			font-size: 12px;
			font-weight: normal;
			line-height: 20px;
			color: #555;
			padding:2px 0;
		}

		.toin {
			border-radius: 4px;
			background: #F5F7FC;
			cursor:pointer;
			display: flex;
			flex-direction: row;
			align-items: center;
			padding:5px;

			&:hover {
				background:#f5f5f5;
			}

			.flex1 {
				flex:1;
			}
		}
	}

}


.ppageWrap {
	margin-bottom:10px;
  position: relative;

  .ppageTransLoading {
    position: absolute;
    left:0;
    top:0;
    background-color: #f60;
    color:#eee;
    padding:5px 10px;
  }

  .ppageTransError {
    position: absolute;
    left:0;
    top:0;
    background-color: #f00;
    color:#eee;
    padding:5px 10px;
  }
}


.langSelectorInWrap {
  width: 232px;
  border-radius: 8px;
  background: #FFF;
  box-sizing: border-box;
  border: 1px solid #EFEFEF;
  box-shadow: 1px 5px 9px 0 rgba(187, 187, 187, 50%);
  padding:10px 0;

  .langSelectorInItem {
    padding:5px 10px;
    cursor: pointer;
    height: 50px;
    display:flex;
    flex-direction: row;
    align-items: center;

    .langSelectorInIteml {
      flex:1;
      display:flex;
      flex-direction: column;
      justify-content: center;
    }

    &:hover {
      background:#f5f5f5;
    }

    .langSelectorInItemTitle {
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: normal;
      color: #000;
    }

    .langSelectorInItemTip {
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
    }

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}


.ppageWraap {
  position: relative;

  .loading {
    position: absolute;
    left:0;
    right:0;
    bottom:0;
    top:0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #eeeeee60;

    .loadingTxt {
      font-size: 12px;
      padding-bottom: 5px;
      padding-left: 5px;
      color:#a1c4f9;
    }
  }
}


.pageDocLoading {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  height: 80px;
  padding-top: 50px;

  .pageDocLoadingTxt {
    padding-bottom: 5px;
    padding-left: 5px;
    font-size: 14px;
  }

  .pageDocLoadingError{
    padding-bottom: 5px;
    padding-left: 5px;
    font-size: 14px;
    color:#f00;
     display: flex;
     flex-direction: column;

     .pageDocLoadingErrorn {
       margin-top:30px;
     }
  }
}


.pageselector {
  margin-right: 5px;

  .pageselectorinput {
    width: 30px;
    font-size: 12px;

    input {
      padding:0;
      text-align: center;
    }
  }

  .pageselectornum {
    font-size: 12px;
  }
}
