import { ConfigWith4A } from '@/config/4AConfig';
import { uploadApi } from '@/services/chat';
import { Toast } from '@douyinfe/semi-ui'; // 如果需要提示消息
import { history } from 'umi'; // 如果需要路由跳转
import umiRequest, { extend, RequestOptionsInit } from 'umi-request';
import { getAccessToken, getTenantId, setAccessToken, setRefreshToken } from './auth';
import { dispatchInUtils } from './dva';

// 基础响应类型（可根据实际后端响应数据调整）
interface OuterBaseResponse<T = any> {
  code: number;
  msg?: string;
  data: T;
}

export interface InnerBaseResponse<T = any> {
  code: number;
  message?: string;
  statusText?: string;
  data: T;
}

interface TypedRequest {
  <T>(url: string, options?: RequestOptionsInit): Promise<OuterBaseResponse<T>>;
}

export enum ErrorMessageType {
  serverError = '服务器错误，请稍后重试',
  streamError = '服务器繁忙，请稍后重试',
  requestError = '请求错误，请稍后重试',
  requestFailError = '请求失败，请稍后重试',
  reportValidFailError = '检测结果为空，请确认报告内容，并重新检测', // 引用标准校验接口异常默认展示
}

const createRequest = <T>() => {
  // 默认请求配置
  const defaultOptions = {
    prefix: '', // 统一前缀
    timeout: 60000, // 超时时间
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 创建请求实例
  const request = extend(defaultOptions);

  // 请求拦截器
  request.interceptors.request.use((url, options) => {
    const finalUrl =
      url.startsWith('http') || url.startsWith('https') ? url : `${process.env.API_URL}${url}`;
    // 在发送请求之前做一些处理，比如添加 token
    //   const token = localStorage.getItem('token');
    const token = getAccessToken();

    const tenantId = getTenantId();
    // if (token) {
    options.headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
      'tenant-id': tenantId,
      'Resource-Code': 'cloud_ai',
    };
    // }
    return { url: finalUrl, options };
  });

  // 响应拦截器
  request.interceptors.response.use(async (response) => {
    const data: OuterBaseResponse<T> = await response.clone().json(); // 克隆响应以读取数据

    // 处理错误状态码
    if (data.code !== 0) {
      if (data.code === 401) {
        // 清空用户信息
        dispatchInUtils({ type: 'auth/reset' });
        // 清理token
        setAccessToken('');
        setRefreshToken('');
        if (process.env.VITE_GLOB_4A_URL) {
          const url = ConfigWith4A.authorizeUrl;
          window.location.href = url;
        } else {
          // 未授权，跳转到登录页
          history.push('/login');
        }
      } else {
        Toast.error(data.msg || ErrorMessageType.requestFailError);
      }
    }

    return response;
  });
  return request;
};

const uupload = async function (afile: any) {
  const formData = new FormData();

  formData.append('path', 'ai/portal/');
  formData.append('configName', 'aifile');
  formData.append('file', afile);

  const uploadUrl = process.env.API_URL + uploadApi;
  return umiRequest(uploadUrl, {
    method: 'POST',
    requestType: 'form', // 关键配置
    data: formData,
  });
};

// 创建带泛型的请求实例

const request = createRequest();
export default request as TypedRequest;

export { uupload };
