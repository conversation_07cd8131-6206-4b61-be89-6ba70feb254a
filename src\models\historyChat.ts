import { getKnowledgeList, queryChatList } from '@/services/chat';
import { Effect, Reducer } from 'umi';

export interface ChatItem {
  chatId: string;
  chatTitle: string;
  appCode: string;
  source: string;
  routeId?: number;
}

export interface KnowledgeItem {
  desc: string;
  name: string;
  url: string;
  routeId: string;
  subTitles: KnowledgeItem[];
}
export interface PPTList {
  list: PPTItem[];
  total: number;
}
export interface PPTItem {
  id: number;
  userId: number;
  userDesignId: string;
  taskId: string;
  title: string;
  thumbnail: string;
  pptUrl: string;
  pdfUrl: string;
  genStatus: number;
  chatId: string;
  chatResId: string;
  createTime: number;
  updateTime: number;
}

export interface PPTData {
  list: PPTItem[];
  total: number;
}
export interface FetchHistoryPPTParams {
  userId: number;
  pageSize: number;
  pageNum: number;
}
export interface HistoryModelState {
  historyList: ChatItem[];
  chatTitle: string;
  appTypeList: KnowledgeItem[];
}

export interface HistoryModelType {
  namespace: 'historyChat';
  state: HistoryModelState;
  effects: {
    fetchHistoryList: Effect;
    fetchKnowledgeList: Effect;
  };
  reducers: {
    save: Reducer<HistoryModelState>;
    unshiftChat: Reducer<HistoryModelState>;
    updateTilte: Reducer<HistoryModelState>;
  };
}

const HistoryChatModel: HistoryModelType = {
  namespace: 'historyChat',

  state: {
    historyList: [],
    chatTitle: '',
    appTypeList: [],
  },

  effects: {
    *fetchHistoryList({ payload }, { call, put }): Generator<any> {
      // 调用 API 获取用户信息
      const response: any = yield call(queryChatList);
      // 更新状态
      yield put({
        type: 'save',
        payload: { historyList: response?.data },
      });
      // 存在chatId更新标题
      if (payload?.chatId) {
        yield put({
          type: 'updateTilte',
          payload,
        });
      }
    },
    *fetchKnowledgeList(_, { call, put }): Generator<any> {
      // 调用 API 获取用户信息
      try {
        const response: any = yield call(getKnowledgeList);
        // 更新状态
        yield put({
          type: 'save',
          payload: { appTypeList: response?.data || [] },
        });
      } catch {
        yield put({
          type: 'save',
          payload: { appTypeList: [] },
        });
      }
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    // 新对话更新历史
    unshiftChat(state, { payload }) {
      const newHistory = state.historyList || [];

      return {
        ...state,
        chatTitle: payload?.chatTitle || '',
        historyList: [{ ...payload }, ...newHistory],
      };
    },
    // 更新标题
    updateTilte(state, { payload }) {
      if (!state?.historyList?.length) {
        return {
          ...state,
        };
      }
      const historyList = state.historyList;
      const target = historyList.find((item) => item.chatId === payload.chatId);
      return {
        ...state,
        chatTitle: target?.chatTitle || '',
      };
    },
  },
};

export default HistoryChatModel;
