import DeleteIcon from '@/assets/txt2img/delete.svg';
import DownloadIcon from '@/assets/txt2img/download.svg';
import LazyImage from '@/components/LazyImage';
import { downloadImagesAsZip } from '@/utils/downloadFile';
import { Button, ImagePreview, Popconfirm } from '@douyinfe/semi-ui';
import React, { useState } from 'react';
import Masonry from 'react-masonry-css';
import styles from './index.less';

export interface ImageItemType {
  id: string;
  name: string;
  url: string;
  width: number;
  height: number;
  tags?: string[];
  images?: string[];
}

const ImgMasonry: React.FC<{
  images: ImageItemType[];
  onDelete: (id: string) => Promise<void>;
}> = ({ images, onDelete }) => {
  const [visible, setVisible] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const breakpointsAndCols = {
    default: 4,
    1100: 3,
    700: 2,
    500: 1,
  };
  // 图片预览触发
  const handleImageClick = (item: ImageItemType) => {
    setPreviewImages(item?.images || [item.url]);
    setVisible(true);
  };
  // 图片预览关闭
  const handleClose = () => {
    setCurrentImageIndex(0);
    setPreviewImages([]);
    setVisible(false);
  };
  // 下载按钮点击
  const handleDownload = (e: React.MouseEvent, item: ImageItemType) => {
    e.stopPropagation();
    downloadImagesAsZip(item.images || [item.url]);
  };
  // 删除按钮点击
  const handleDelete = (item: ImageItemType) => {
    onDelete(item.id);
  };
  return (
    <div className={styles.imgMasonry}>
      <Masonry
        breakpointCols={breakpointsAndCols}
        className={styles.masonryGrid}
        columnClassName={styles.masonryGridColumn}
      >
        {images.map((item) => (
          <div key={item.id} className={styles.galleryItem} onClick={() => handleImageClick(item)}>
            <LazyImage url={item.url} alt={item.name || ''} height={item.height} />
            {/* 操作按钮区域：位于图片容器内部，但通过stopPropagation隔离事件 */}
            <div className={styles.imageActions}>
              <Button
                className={styles.actionBtn}
                icon={<img src={DownloadIcon} alt="下载" />}
                onClick={(e) => handleDownload(e, item)}
              />
              <Popconfirm
                title="确定要删除此条记录吗？"
                content="删除后，内容无法恢复。"
                onConfirm={() => handleDelete(item)}
              >
                <Button
                  className={`${styles.actionBtn} ${styles.deleteBtn}`}
                  icon={<img src={DeleteIcon} alt="删除" />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Popconfirm>
            </div>
          </div>
        ))}
      </Masonry>
      <ImagePreview
        src={previewImages}
        currentIndex={currentImageIndex}
        onChange={(index) => setCurrentImageIndex(index)}
        visible={visible}
        onClose={handleClose}
        onVisibleChange={(visible) => {
          setVisible(visible);
          if (!visible) {
            handleClose();
          }
        }}
        preLoad={true}
      />
    </div>
  );
};

export default ImgMasonry;
