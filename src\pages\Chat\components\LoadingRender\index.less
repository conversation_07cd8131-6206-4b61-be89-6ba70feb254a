.loadingContainer {
  // display: flex;
  // align-items: center;
  // line-height: 0;
}

.isReasoningLoading {
  display: flex;
  align-items: center;
  line-height: 0;

  .isReasoningLoadingText{
    font-size: 14px;
    color: #999;
    margin-left: 4px;
  }

  :global(.semi-spin-wrapper) {
    color: #999;
  }
}

.imgGeneratingContainer {
  max-width: 748px;
  display: inline-flex;
  align-items: center;
  background: #F6F8FD;
  padding: 8px;
  border-radius: 8px;
  margin-top: 3px;
}

.imgGeneratingList {
  display: flex;
  gap: 8px;
  // justify-content: center;
  flex-wrap: wrap;
}

.imgGeneratingItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 174px;
  height: 261px;
}

.imgGeneratingProgressBg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 103px;
  height: 4px;
  border-radius: 4px;
  background: #CCD3E8;
  z-index: 2;
  overflow: hidden;
}

.imgGeneratingProgressBar {
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(90deg, #00B2F8 0%, #005BF8 104%);
  transition: width 0.4s cubic-bezier(0.4,0,0.2,1);
  position: relative;
  overflow: hidden;
}

.imgGeneratingProgressBarShine {
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 100%;
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(
    120deg,
    transparent 0%,
    rgba(255,255,255,22%) 40%,
    rgba(255,255,255,45%) 50%,
    rgba(255,255,255,22%) 60%,
    transparent 100%
  );
  background-size: 32px 100%;
  background-repeat: repeat-x;
  animation: progress-shine-move 0.9s linear infinite;
  opacity: 0.4;
}

@keyframes progress-shine-move {
  0% { background-position: 0 0; }
  100% { background-position: 32px 0; }
}

.imgGeneratingImg {
  width: 174px;
  height: 261px;
  object-fit: cover;
  display: block;
  border-radius: 4px;

  :global {
    .semi-image-img {
      width: 100% !important;
      height: 100% !important
    }
  }
}
// 生成中的加载状态动画
.imgGeneratingBar {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background: linear-gradient(152deg, #E2F2F8 3%, #CCD9F9 93%);

  &::before {
    content: '';
    position: absolute;
    top: -65%;
    left: -65%;
    width: 230%;
    height: 230%;
    background:
      linear-gradient(
        135deg,
        rgba(194, 221, 255, 0%) 18%,
        rgba(194, 221, 255, 10%) 32%,
        rgba(194, 221, 255, 18%) 44%,
        rgba(255,255,255,18%) 48%,
        rgba(255,255,255,32%) 50%,
        rgba(255,255,255,18%) 52%,
        rgba(194, 221, 255, 18%) 56%,
        rgba(194, 221, 255, 10%) 68%,
        rgba(194, 221, 255, 0%) 82%
      );
    filter: blur(2px);
    transform: translate(0, 0);
    animation: img-bar-shine 1.8s linear infinite;
    pointer-events: none;
    z-index: 2;
  }
}

@keyframes img-bar-shine {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(75%, 75%);
  }
}
