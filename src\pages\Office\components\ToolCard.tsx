import styles from '@/styles/officetemplate-common.less';
import { Card, Col } from '@douyinfe/semi-ui';
import React from 'react';

interface ToolCardProps {
  tool: any;
  index: number;
  selectedIndex: number | null;
  onClick: (index: number) => void;
}

const ToolCard: React.FC<ToolCardProps> = ({ tool, index, selectedIndex, onClick }) => {
  return (
    <Col span={6} style={{ padding: '4px' }}>
      <div onClick={() => onClick(index)} style={{ cursor: 'pointer' }}>
        <Card
          className={`${styles.toolCard} ${selectedIndex === index ? styles.selectedCard : ''}`}
          shadows="hover"
          bodyStyle={{ padding: 16 }}
        >
          <div className={styles.cardContent}>
            <div className={styles.iconPlaceholder}>
              {tool.icon || <div className={styles.defaultIcon}></div>}
            </div>
            <div className={styles.textContent}>
              <div className={styles.templateTitle}>
                {tool.title && typeof tool.title === 'string'
                  ? tool.title
                  : tool.desc && typeof tool.desc === 'string'
                  ? tool.desc
                  : '工具' + (index + 1)}
              </div>
              <div className={styles.templateDesc}>
                {tool.desc && typeof tool.desc === 'string' && tool.desc !== tool.title
                  ? tool.desc
                  : '自动生成内容'}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </Col>
  );
};

export default ToolCard;
