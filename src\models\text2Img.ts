import { fetchTextImageMenuList, TextImageMenuList } from '@/services/text2Img';
import { Effect, Reducer } from 'umi';

export interface Text2ImgModelState {
  navList: TextImageMenuList[];
}

export interface ModelType {
  namespace: 'text2Img';
  state: Text2ImgModelState;
  effects: {
    fetchTextImageMenuList: Effect;
  };
  reducers: {
    save: Reducer<Text2ImgModelState>;
  };
}

const text2ImgModel: ModelType = {
  namespace: 'text2Img',

  state: {
    navList: [],
  },

  effects: {
    *fetchTextImageMenuList(_, { call, put }): Generator<any> {
      // 调用 API 获取文生图二级菜单
      const response: any = yield call(fetchTextImageMenuList);
      // 更新状态
      yield put({
        type: 'save',
        payload: { navList: response?.data },
      });
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};

export default text2ImgModel;
