import type { ChatMessageContent } from '@/pages/Chat/types';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';

/**
 * 获取历史对话接口定义
 */
export interface ChatItem {
  chatId: string;
  chatTitle: string;
  appCode: string;
  routeId?: number;
}

export interface QueryChatListItem {
  dataId: string;
  hideInUI: boolean;
  obj: 'Human' | 'AI';
  time: string;
  value: ChatMessageContent[];
  userBadFeedback: 'yes' | boolean;
  userGoodFeedback: 'yes' | boolean;
}

export interface GuessYouWantParams {
  appCode: string;
  chatId: string;
}

export interface FetchChatStreamParams {
  search: any;
  userId: string | number | undefined;
  reqChatDataId: string;
  resChatDataId: string;
  chatId: string;
  appCode: string;
  content: any;
  fileTypes: FileItem[];
  routeId?: string | number;
  batchSize?: string;
  color?: string;
  ratio?: string;
  style?: string;
  userInput?: string;
}

export interface ChatPathParams {
  appCode: string;
}

export interface EnginItem {
  routeId: number;
  desc: string;
  appCode: string;
  show: boolean;
  childrens?: EnginItem[];
}

export interface FeedBackInter {
  appCode: string;
  chatId: string;
  resDataId: string;
  feeBackType: number;
  yesOrNot: boolean;
}

export interface ImageTaskAbortParams {
  chatId: string;
  resChatDataId: string;
}
