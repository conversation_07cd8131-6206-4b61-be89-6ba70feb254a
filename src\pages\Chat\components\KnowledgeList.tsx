import { HistoryModelState, KnowledgeItem } from '@/models/historyChat';
import { removeInternationalProjectPrefix } from '@/utils';
import { Button, Dropdown } from '@douyinfe/semi-ui';
import React from 'react';
import { useSelector } from 'umi';
import styles from './index.less';

// export interface KnowledgeItem {
//   desc: string;
//   name: string;
//   url: string;
//   routeId: string;
//   subTitles: KnowledgeItem[];
// }

const renderButton = (app: KnowledgeItem, props: { onChange: any }) => {
  return (
    <Button
      key={app.routeId}
      theme="outline"
      type="tertiary"
      icon={<img src={app.url} />}
      onClick={app.subTitles.length > 0 ? undefined : () => props.onChange(app)}
    >
      {app.desc}
    </Button>
  );
};
/**
 * 知识库顶部选项
 * @returns
 */
const KnowledgeList: React.FC<{
  onChange: (item: KnowledgeItem) => void;
}> = (props) => {
  const { appTypeList } = useSelector(
    (state: { historyChat: HistoryModelState }) => state.historyChat,
  );
  return (
    <div className={styles.appEntrance}>
      {!!appTypeList.length &&
        appTypeList.map((app) => {
          return app.subTitles && app.subTitles.length > 0 ? (
            <Dropdown
              trigger={'click'}
              position={'topLeft'}
              clickToHide={true}
              key={app.routeId}
              className={styles.dropdown}
              render={
                <Dropdown.Menu className={styles.dropdownMenu}>
                  {app.subTitles?.map((item) => {
                    item.url = item.url || app.url;
                    return (
                      <Dropdown.Item
                        className={styles.dropdownItem}
                        // icon={<img src={item.url} />}
                        onClick={() => props.onChange(item)}
                        key={item.routeId}
                      >
                        {removeInternationalProjectPrefix(item.desc)}
                      </Dropdown.Item>
                    );
                  })}
                </Dropdown.Menu>
              }
            >
              {renderButton(app, props)}
            </Dropdown>
          ) : (
            renderButton(app, props)
          );
        })}
    </div>
  );
};

export default KnowledgeList;
