import { login } from '@/services/login';
import { getAccessToken, setAccessToken, setRefreshToken } from '@/utils/auth';
import { Button, Form, Toast, useFormApi } from '@douyinfe/semi-ui';
import React, { useEffect } from 'react';
import { useNavigate } from 'umi';
import styles from './index.less';

const Login: React.FC = () => {
  let navigate = useNavigate();

  useEffect(() => {
    // 获取 token
    const token = getAccessToken();

    if (token) {
      navigate('/chat');
    }
  }, [navigate]);

  /**
   * 登录
   * @returns
   */
  const ComponentUsingFormApi = () => {
    const formApi = useFormApi();
    const handleLogin = () => {
      const params = formApi.getValue();

      if (!params.username || !params.password) {
        Toast.error('请输入账号或密码');
        return;
      }
      login({
        ...params,
      })
        .then((res) => {
          setAccessToken(res.data.accessToken);
          setRefreshToken(res.data.refreshToken);
          navigate('/chat');
        })
        .catch((err) => {
          console.log('🚀 ~ handleLogin ~ err:', err);
          // Toast.error(err.msg);
        });
    };

    const keyDownevent = (e: KeyboardEvent) => {
      if (e.keyCode === 13) {
        handleLogin();
      }
    };

    const addEvent = () => {
      document.addEventListener('keydown', keyDownevent);
    };

    const removeEvent = () => {
      document.removeEventListener('keydown', keyDownevent);
    };

    useEffect(() => {
      addEvent();
      return removeEvent;
    }, []);

    return (
      <Button
        onClick={handleLogin}
        style={{ width: '100%', marginTop: 12 }}
        type="primary"
        size="large"
      >
        登录
      </Button>
    );
  };

  return (
    <div className={styles.loginPage} onKeyDown={onkeydown}>
      <div className={styles.loginPage_form}>
        <h3>瞳界AI应用平台</h3>
        <Form>
          <Form.Input
            field="username"
            noLabel={true}
            label="Username"
            placeholder="账号"
            size="large"
          />
          <Form.Input
            field="password"
            noLabel={true}
            label="Password"
            type="password"
            size="large"
            placeholder="密码"
          />
          <ComponentUsingFormApi />
        </Form>
      </div>
    </div>
  );
};

export default Login;
