import request from '@/utils/request';

export const pptCodeApi = '/ai/ppt/getCode';

export const aiPPTSaveApi = '/ai/ppt/aiPptSave';

export const aiPPTByChatIdAndResIdApi = '/ai/ppt/getAiPptByChatIdAndResId';

/**
 * 获取租户id
 * @param code
 * @returns
 */
export function getPPTCode() {
  return request(pptCodeApi, {
    method: 'GET',
  });
}

/**
 * ppt保存修改
 */
export const fetchAiPptSave = (data: any) =>
  request<{
    taskId: '';
    thumbnail?: '';
  }>(aiPPTSaveApi, {
    method: 'POST',
    data,
  });

/**
 * 获取AIPPT记录
 */
export const fetchAiPptByChatIdAndResId = (params: any) =>
  request(aiPPTByChatIdAndResIdApi, { params });
