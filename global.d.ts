interface showOptions {
  /**
   * 授权参数
   * @requires
   * @description 授权参数
   */
  appkey: string;
  /**
   * 授权参数 (没有则传空)
   * @description 授权参数` (没有则传空)`
   */
  channel: string;
  /**
   * @description 授权参数
   */
  code: string;
  /**
   * @description 是否可编辑PPT模板功能
   * @default false
   */
  editorModel: boolean;
  /**
   * iframe挂载的目标DOM元素,如不传默认挂载到Body
   * @description 授权参数` (没有则传空)`
   * @default body
   */
  container: HTMLElement;
  /**
   * @description 可选配置项
   */
  options: Record<string, any>;
  /**
   * @description 路由可选配置项
   */
  routerOptions: Record<string, any>;
  /**
   * aippt事件回调
   * @param eventType
   * @param callback
   */
  onMessage(eventType: string, data: Record<string, any>): void;
}

/**
 * @file aippt
 * @description Aippt iframe sdk
 * * @url https://open.aippt.cn/docs/zh/ui/access.html
 */
declare const AipptIframe: {
  /**
   * 挂载iframe
   * @param options showOptions
   * @url https://open.aippt.cn/docs/zh/ui/access.html
   */
  show(options: showOptions): void;

  /**
   * 卸载iframe
   */
  deleteIframe(): void;
};
