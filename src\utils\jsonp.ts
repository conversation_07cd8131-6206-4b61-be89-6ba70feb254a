// 扩展 Window 接口以支持动态属性
interface Window {
    [key: string]: any;
}

// 定义 JSONP 回调函数的类型
type JsonpCallback = (result: unknown) => void;

export const jsonp = function (url: string, data: any): Promise<unknown> {
    return new Promise((resolve, reject) => {
        // 初始化 URL
        let dataString = url.indexOf('?') === -1 ? '?' : '&';
        let callbackName = `jsonpCB_${Date.now()}`;
        url += `${dataString}callback=${callbackName}`;

        if (data) {
            // 有请求参数，依次添加到 URL
            for (let k in data) {
                url += `&${k}=${encodeURIComponent(data[k])}`; // 使用 encodeURIComponent 防止特殊字符问题
            }
        }

        let scriptNode = document.createElement('script');
        scriptNode.src = url;

        // 定义 JSONP 回调函数
        (window as Window)[callbackName] = ((result: unknown): void => {
            if (result) {
                resolve(result);
            } else {
                reject('没有返回数据');
            }
            delete (window as Window)[callbackName];
            document.body.removeChild(scriptNode);
        }) as JsonpCallback;

        // 处理异常情况
        scriptNode.addEventListener('error', () => {
            reject('接口返回数据失败');
            delete (window as Window)[callbackName];
            document.body.removeChild(scriptNode);
        });

        // 开始请求
        document.body.appendChild(scriptNode);
    });
};