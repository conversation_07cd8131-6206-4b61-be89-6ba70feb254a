import { Descendant } from 'slate';

/**
 * 验证Slate节点结构是否有效
 * @param nodes 要验证的节点数组
 * @returns 验证后的安全节点数组
 */
export const validateSlateNodes = (nodes: any[]): Descendant[] => {
  if (!Array.isArray(nodes) || nodes.length === 0) {
    return [
      {
        type: 'paragraph',
        children: [{ text: '' }],
      },
    ];
  }

  return nodes.map((node: any) => {
    // 检查是否是Element节点（有type和children属性）
    if (node.type) {
      // 确保Element节点有有效的children数组
      if (!node.children || !Array.isArray(node.children) || node.children.length === 0) {
        return {
          ...node,
          children: [{ text: '' }],
        };
      }

      // 验证children中的每个节点
      const validatedChildren = node.children.map((child: any) => {
        // 如果child是Element节点，递归验证
        if (child.type) {
          return validateSlateNodes([child])[0];
        }
        // 如果是Text节点，确保有text属性
        if (typeof child.text !== 'string') {
          return { ...child, text: '' };
        }
        return child;
      });

      return {
        ...node,
        children: validatedChildren,
      };
    }

    // 如果是Text节点，确保有text属性
    if (typeof node.text !== 'string') {
      return { ...node, text: '' };
    }

    return node;
  });
};

/**
 * 创建安全的初始Slate值
 * @returns 安全的初始值
 */
export const createSafeInitialValue = (): Descendant[] => {
  return [
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ];
};

/**
 * 安全地重置Slate编辑器
 * @param editor Slate编辑器实例
 */
export const safeResetSlateEditor = (editor: any) => {
  try {
    if (editor && editor.children) {
      editor.children = createSafeInitialValue();
      editor.selection = null;
    }
  } catch (error) {
    console.error('Error resetting Slate editor:', error);
    // 强制重置
    if (editor) {
      editor.children = [{ type: 'paragraph', children: [{ text: '' }] }];
      editor.selection = null;
    }
  }
};
