import backgroundColor from '@/assets/chat/backgroundColor.svg';
import countIcon from '@/assets/chat/count.svg';
import disablesBackgroundColor from '@/assets/chat/disablesBackgroundColor.svg';
import disablesCountIcon from '@/assets/chat/disablesCountIcon.svg';
import disablesImageIcon from '@/assets/chat/disablesImageIcon.svg';
import disablesRatioIcon from '@/assets/chat/disablesRatioIcon.svg';
import disablesStyleIcon from '@/assets/chat/disablesStyleIcon.svg';
import imageIcon from '@/assets/chat/image.svg';
import ratioIcon from '@/assets/chat/ratio.svg';
import styleIcon from '@/assets/chat/styleimg.svg';

import { TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID, TEXT_CREATION_IMAGE_COUNT } from '@/config';
import { Text2ImgModelState } from '@/models/text2Img';
import UploadComponent from '@/pages/Chat/components/Upload';
import { allValuesNotEmpty, dispatchInUtils } from '@/utils';
import { Button, Dropdown, Toast, Tooltip } from '@douyinfe/semi-ui';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useDispatch, useSearchParams, useSelector } from 'umi';
import styles from './index.less';

// 类型定义：明确数据结构，提升类型安全
interface DropdownItem {
  code: string;
  name: string;
  icon?: string;
}

interface ImageConfig {
  content?: string | null;
  userInput?: string | null;
  ratio?: string | null;
  batchSize?: string | null;
  color?: string | null;
  style?: string | null;
  configText?: string | null;
}

interface NavItem {
  routeId?: number;
  ratios?: DropdownItem[];
  styles?: DropdownItem[];
  color?: DropdownItem[];
  batchSizes?: any[];
}

interface SmartUploadItemProps {
  onSuccess: (resp: any, file: File, fileType: string) => void;
  content: string;
  fileList: any[];
  setFileList: (fileList: any[]) => void;
  uploadProgress: (percent: number, file: File, fileTag?: string) => void;
  chatId: string;
  onImgConfigChange: (config: boolean) => void;
  appCode: string;
}

// 图标映射：统一管理图标资源
const ICON_MAP: any = {
  ratio: {
    normal: ratioIcon,
    disabled: disablesRatioIcon,
  },
  style: {
    normal: styleIcon,
    disabled: disablesStyleIcon,
  },
  batchSize: {
    normal: countIcon,
    disabled: disablesCountIcon,
  },
  image: {
    normal: imageIcon,
    disabled: disablesImageIcon,
  },
  color: {
    normal: backgroundColor,
    disabled: disablesBackgroundColor,
  },
};

// 下拉框配置：集中管理下拉框类型配置
const DROPDOWN_TYPES = {
  ratio: {
    label: '比例',
    routeKey: 'image_ratio',
  },
  style: {
    label: '风格',
    routeKey: 'image_style',
  },
  batchSize: {
    label: '个数',
    routeKey: 'image_count',
  },
  color: {
    label: '背景颜色',
    routeKey: 'image_color',
  },
};

const TextCreationImageButton = forwardRef((props: SmartUploadItemProps, ref) => {
  const {
    onSuccess,
    content,
    uploadProgress,
    fileList,
    setFileList,
    chatId,
    onImgConfigChange,
    appCode,
  } = props;
  const dispatch = useDispatch();

  // const chatState = useSelector((state: { chat: ChatModelState }) => state.chat.chatMsg);//getTextImageMenuList 多次请求暂时隐藏

  // Redux状态
  const imageRouteId = useSelector((state: any) => state.chat.imageRouteId);
  const { navList } = useSelector((state: { text2Img: Text2ImgModelState }) => state.text2Img);
  const [searchParams, setSearchParams] = useSearchParams();
  const routeId = searchParams.get('routeId') || searchParams.get('code');

  const fileSize = 6;

  // 状态管理
  const [imageConfig, setImageConfig] = useState<ImageConfig>({
    content: null,
    userInput: null,
    ratio: null,
    batchSize: null,
    color: null,
    style: null,
    configText: null,
  });
  const [selectedValues, setSelectedValues] = useState<Record<string, string>>({
    ratio: '',
    style: '',
    batchSize: '',
    color: '',
  });
  const [options, setOptions] = useState<Record<string, DropdownItem[]>>({
    ratio: [],
    style: [],
    batchSize: [],
    color: [],
  });

  const reset = () => {
    // 重置选中值
    setSelectedValues({
      ratio: '',
      style: '',
      batchSize: '',
      color: '',
    });
    setFileList([]);
    setImageConfig({
      content: null,
      userInput: null,
      ratio: null,
      batchSize: null,
      color: null,
      style: null,
      configText: null,
    });
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getImgConfig: () => imageConfig,
    getSelectedValues: () => selectedValues,
    getReset: reset,
  }));

  // 初始化文生图Tab
  useEffect(() => {
    dispatchInUtils({
      type: 'text2Img/fetchTextImageMenuList',
    });
  }, []);
  /*getTextImageMenuList 多次请求暂时隐藏
  // useEffect(() => {
  //   dispatchInUtils({
  //     type: 'text2Img/fetchTextImageMenuLisItem',
  //     payload: {
  //       routeId: chatId,
  //     },
  //   });
  // }, [chatState]);

  // // 监听imageRouteId并存储最新值
  // useEffect(() => {
  //   if (imageRouteId) {
  //     dispatchInUtils({
  //       type: 'text2Img/fetchTextImageMenuLisItem',
  //       payload: {
  //         routeId: imageRouteId,
  //       },
  //     });
  //   }
  // }, [imageRouteId]);
  */
  // 初始化选项数据
  useEffect(() => {
    const currentNav: NavItem =
      (navList && navList.find((item: any) => item.routeId === Number(imageRouteId || routeId))) ||
      {};
    setOptions({
      ratio: currentNav.ratios || [],
      style: currentNav.styles || [],
      color: currentNav.color || [],
      batchSize: currentNav.batchSizes || [],
    });
    reset();
  }, [imageRouteId, navList]);

  // 更新图片配置内容
  useEffect(() => {
    const configText = Object.entries(selectedValues)
      .filter(([_, value]) => value)
      .map(
        ([type, value]) => `${DROPDOWN_TYPES[type as keyof typeof DROPDOWN_TYPES].label}${value}`,
      )
      .join(', ');
    if (content) {
      setImageConfig((prev) => ({
        ...prev,
        configText: configText,
        userInput: content,
      }));
    } else {
      setImageConfig((prev) => ({
        ...prev,
        configText: configText,
      }));
    }
  }, [selectedValues, content]);

  // 监听content变化用于处理发送按钮禁用状态
  useEffect(() => {
    const config = allValuesNotEmpty(imageConfig);
    onImgConfigChange(checkFields(fileList.length, config, content, imageRouteId || routeId));
  }, [content, fileList]);

  // 参数按钮点击
  const handleSelectChange = (item: any, type: string) => {
    setSelectedValues((prev) => ({ ...prev, [type]: item.name }));
    // 更新到本页的imgConfig
    setImageConfig((prev) => ({ ...prev, [type]: item.code }));
  };

  // 根据conteng imageConfig fileList 判断发送按钮状态
  function checkFields(
    uploadedFilesLength: number, // 文件列表长度
    hasSettings: boolean, // 是否有设置
    promptText: string, // 提示文本
    _routerId: string | null, // ID
  ) {
    if (Boolean(hasSettings)) {
      return true;
    }
    if (Boolean(promptText)) {
      return true;
    }
    if (Boolean(uploadedFilesLength)) {
      if (TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID.image_text.includes(_routerId as string)) {
        return true;
      }
      return false;
    }
    return false;
  }

  // 保存至store
  useEffect(() => {
    dispatch({
      type: 'chat/setImageConfig',
      payload: imageConfig,
    });
    const config = allValuesNotEmpty(imageConfig);
    onImgConfigChange(checkFields(fileList.length, config, content, imageRouteId || routeId));
  }, [imageConfig]);

  // 通用下拉框组件：减少重复代码
  const renderDropdown = (type: keyof typeof DROPDOWN_TYPES, index: number) => {
    const { label, routeKey } = DROPDOWN_TYPES[type];
    const isVisible = TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID[
      routeKey as keyof typeof TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID
    ].includes(`${imageRouteId || routeId}`);

    if (!isVisible) return null;

    const items = options[type];

    return (
      <Dropdown
        key={index}
        trigger="click"
        position="top"
        clickToHide
        render={
          <Dropdown.Menu>
            {items.map((item: any) => (
              <Dropdown.Item key={item.code} onClick={() => handleSelectChange(item, type)}>
                <div className={type === 'batchSize' ? '' : styles.itemInfo}>
                  {item.icon ? (
                    <div className={`${styles.itemIcon} ${type === 'ratio' && styles.itemWidth}`}>
                      <img src={item.icon} />
                    </div>
                  ) : (
                    ''
                  )}

                  {item.colorNumber && (
                    <div
                      className={styles.itemColor}
                      style={{ backgroundColor: item.colorNumber }}
                    ></div>
                  )}
                  <div className={styles.itemDescribe}>{item.name}</div>
                </div>
              </Dropdown.Item>
            ))}
          </Dropdown.Menu>
        }
      >
        <Button
          theme="borderless"
          type="tertiary"
          className={styles.onlineQueryBtn}
          icon={
            <img
              // src={selectedValues[type] ? ICON_MAP[type].disabled : ICON_MAP[type].normal}
              src={ICON_MAP[type].normal}
              alt={label}
            />
          }
        >
          {selectedValues[type] || label}
          {/* {type === 'batchSize' && selectedValues[type] ? '个' : ''} */}
        </Button>
      </Dropdown>
    );
  };

  // 检查是否显示原图片/参考图文本
  const isShowNativeText =
    TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID.image_text.includes(`${imageRouteId || routeId}`) &&
    appCode === 'image';

  return (
    <div className="flex items-center gap-x-[8px] gap-y-[8px]">
      {/* 图片上传组件 */}
      {TEXT_CREATION_IMAGE_BUTTON_ROUTE_ID.image.includes(`${imageRouteId || routeId}`) && (
        <UploadComponent
          key="file-upload-standard"
          accept=".jpg,.jpeg,.png,.webp"
          onExceed={() => {
            if (fileList?.length > TEXT_CREATION_IMAGE_COUNT) {
              Toast.info(`最多只能上传${TEXT_CREATION_IMAGE_COUNT}个图片`);
            }
          }}
          limit={fileList?.length ? 0 : TEXT_CREATION_IMAGE_COUNT}
          onProgress={(percent, file) => uploadProgress(percent, file)}
          onSuccess={(resp: any, file: any) => onSuccess(resp, file, 'image')}
          propsClassName={styles.uploadFile}
          isIcon
          fileSize={fileSize}
          UploadButton={
            <Tooltip
              content={`最多上传1个图片，支持格式：jpg、jpeg、png、webp，单个文件大小不超过${fileSize}MB`}
            >
              <div>
                <Button
                  theme="borderless"
                  type="tertiary"
                  className={styles.onlineQueryBtn}
                  onClick={(e) => e.currentTarget.blur()}
                  disabled={!!fileList?.length}
                  icon={
                    <img
                      src={fileList?.length ? ICON_MAP.image.disabled : ICON_MAP.image.normal}
                      alt="图片"
                    />
                  }
                >
                  {isShowNativeText ? '原图片' : '参考图'}
                </Button>
              </div>
            </Tooltip>
          }
        />
      )}

      {/* 渲染所有类型的下拉框 */}
      {Object.keys(DROPDOWN_TYPES).map((type, index) =>
        renderDropdown(type as keyof typeof DROPDOWN_TYPES, index),
      )}
    </div>
  );
});

export default TextCreationImageButton;
