window.VditorI18n = {
  alignCenter: '居中',
  alignLeft: '居左',
  alignRight: '居右',
  alternateText: '替代文本',
  bold: '粗体',
  both: '编辑 & 预览',
  check: '任务列表',
  close: '关闭',
  code: '代码块',
  'code-theme': '代码块主题预览',
  column: '列',
  comment: '评论',
  confirm: '确定',
  'content-theme': '内容主题预览',
  copied: '已复制',
  copy: '复制',
  'delete-column': '删除列',
  'delete-row': '删除行',
  devtools: '开发者工具',
  down: '下',
  downloadTip: '该浏览器不支持下载功能',
  edit: '编辑',
  'edit-mode': '切换编辑模式',
  emoji: '表情',
  export: '导出',
  fileTypeError: '文件类型不允许上传，请压缩后再试',
  footnoteRef: '脚注标识',
  fullscreen: '全屏切换',
  generate: '生成中',
  headings: '标题',
  heading1: '一级标题',
  heading2: '二级标题',
  heading3: '三级标题',
  heading4: '四级标题',
  heading5: '五级标题',
  heading6: '六级标题',
  help: '帮助',
  imageURL: '图片地址',
  indent: '列表缩进',
  info: '关于',
  'inline-code': '行内代码',
  'insert-after': '末尾插入行',
  'insert-before': '起始插入行',
  insertColumnLeft: '在左边插入一列',
  insertColumnRight: '在右边插入一列',
  insertRowAbove: '在上方插入一行',
  insertRowBelow: '在下方插入一行',
  instantRendering: '即时渲染',
  italic: '斜体',
  language: '语言',
  line: '分隔线',
  link: '链接',
  linkRef: '引用标识',
  list: '无序列表',
  more: '更多',
  nameEmpty: '文件名不能为空',
  'ordered-list': '有序列表',
  outdent: '列表反向缩进',
  outline: '大纲',
  over: '超过',
  performanceTip: '实时预览需 ${x}ms，可点击编辑 & 预览按钮进行关闭',
  preview: '预览',
  quote: '引用',
  record: '开始录音/结束录音',
  'record-tip': '该设备不支持录音功能',
  recording: '录音中...',
  redo: '重做',
  remove: '删除',
  row: '行',
  spin: '旋转',
  splitView: '分屏预览',
  strike: '删除线',
  table: '表格',
  textIsNotEmpty: '文本（不能为空）',
  title: '标题',
  tooltipText: '提示文本',
  undo: '撤销',
  up: '上',
  update: '更新',
  upload: '上传图片',
  uploadError: '上传错误',
  uploading: '上传中...',
  wysiwyg: '所见即所得',
};
