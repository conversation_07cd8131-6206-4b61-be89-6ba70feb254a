/**
 * 知识库
 */
import { GreetingRender } from '@/pages/Chat/components/Greeting';
import { fetchNewChatId } from '@/services/chat';
import { dispatchInUtils } from '@/utils';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { useState } from 'react';
import { useNavigate } from 'umi';
import AIChat from '../Chat/Chat';
import styles from '../Chat/index.less';
import type { QuestionsData } from '../Chat/types';
const DocsPage: React.FC = () => {
  const navigate = useNavigate();
  const modeCode = 'knowledge';
  /**
   * 分类code
   */
  const [appInfo, setAppInfo] = useState<any>({});
  const customHandleSend = async (content: string, attachments: FileItem[]) => {
    try {
      const params = {
        appCode: modeCode, // 默认code，选择知识库后替换
      };

      const res = await fetchNewChatId(params);
      const stateData: QuestionsData = {
        content,
        attachments,
      };
      if (res.data) {
        dispatchInUtils({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: content.substring(0, 20) || '新对话',
            appCode: modeCode,
            source: '',
          },
        });
        // type是菜单类型，from是来源，code是智能体二级分类
        const queryParams: Record<string, string> = {
          type: params.appCode,
          from: modeCode,
        };

        if (appInfo?.routeId) {
          queryParams.code = appInfo?.routeId;
        }

        const queryString = new URLSearchParams(queryParams).toString();

        navigate(`/chat/${res.data}?${queryString}`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };
  const chatTopSlot = () => {
    return (
      <>
        <GreetingRender />
      </>
    );
  };
  return (
    <div className={styles.homeChat}>
      <AIChat
        customChatFunction={{
          onMenuChange: setAppInfo,
        }}
        chatTopSlot={chatTopSlot()}
        customHandleSend={customHandleSend}
        showTemplateHeader={false}
        appCode={modeCode}
      />
    </div>
  );
};

export default () => <DocsPage />;
