import { PPTItem } from '@/models/historyChat';
import { getState } from '@/utils';
import { Toast } from '@douyinfe/semi-ui';
import React, { useEffect, useState } from 'react';
import styles from '../../../styles/officetemplate-common.less';
import { NavMenu, ToolList } from '../components';
import { useOfficeTemplate } from '../hooks/useOfficeTemplate';
import { deletePPT } from '../office';
import ChatPpt from './HistoryPPT';
import ReadingPannel from './ReadingPannel';

interface TemplateContentProps {
  currNav: string;
  changeNav?: (nav: string) => void;
  onSelectTemplate?: (template: any) => void;
}

const TemplateContent: React.FC<TemplateContentProps> = ({
  currNav,
  changeNav,
  onSelectTemplate,
}) => {
  const {
    activeNav,
    tools,
    loading,
    selectedCardIndex,
    disabledNavs,
    handleNavClick,
    handleTemplateSelect,
    pptList,
    pptListTotal,
    pptLoading,
    getHistoryPPTData,
  } = useOfficeTemplate({ currNav, changeNav, onSelectTemplate });

  // 智能PPT下载方法
  const handleDownload = async (item: PPTItem) => {
    try {
      const response = await fetch(item.pptUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = item.title;
      a.click();

      URL.revokeObjectURL(url);
      Toast.success('下载完成');
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  // 智能PPT删除方法
  const handleDelete = async (item: PPTItem) => {
    const { data } = await deletePPT(item.id);

    if (data) {
      Toast.success('删除成功');
      const user = getState().auth.user;
      await getHistoryPPTData(user.id, pageSize, pageNum); // 调用 getHistoryPPTData 方法更新数据
    }
  };

  // 智能PPT跳转方法
  const handleItemClick = (item: PPTItem) => {
    if (item.pdfUrl) {
      window.open(
        `/ppt-view?url=${item.pdfUrl}&name=${encodeURIComponent(item?.title || '')}`,
        '_blank',
      );
    } else {
      console.error('pdfUrl 或 title 为空，无法打开新窗口');
    }
  };
  const [pageNum, setPageNum] = useState(1);
  const pageSize = 9;
  // 分页页码改变
  const handlePageChange = async (pageNum: number) => {
    const user = getState().auth.user;
    setPageNum(pageNum);
    await getHistoryPPTData(user.id, pageSize, pageNum); // 调用 getHistoryPPTData 方法更新数据
  };

  useEffect(() => {
    setPageNum(1); // 重置页码为1
  }, [activeNav]);

  let contentTemplate = null;
  if (currNav === 'write') {
    contentTemplate = (
      <ToolList
        loading={loading}
        tools={tools}
        selectedCardIndex={selectedCardIndex}
        onCardClick={handleTemplateSelect}
      />
    );
  } else if (currNav === 'reading') {
    contentTemplate = <ReadingPannel />;
  } else if (activeNav === 'ppt') {
    contentTemplate = (
      <ChatPpt
        loading={pptLoading}
        tools={pptList}
        total={pptListTotal}
        onDownload={handleDownload}
        onDelete={handleDelete}
        onItemClick={handleItemClick}
        page={pageNum}
        onPageChange={handlePageChange}
        pageSize={pageSize}
      />
    );
  }

  return (
    <div className={styles.templateContainer}>
      <NavMenu currentNav={currNav} onNavClick={handleNavClick} disabledNavs={disabledNavs} />
      <div className={styles.templateContainerMain}>{contentTemplate}</div>
    </div>
  );
};

export default TemplateContent;
