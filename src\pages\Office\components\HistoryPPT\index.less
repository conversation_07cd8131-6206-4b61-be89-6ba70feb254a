.chatpptmain {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: none;

  // :global {

  //   .semi-page-item {
  //     width: 24px;
  //     height: 24px;
  //     min-width: 24px;
  //     border-radius: 3px;
  //     /* Gray 中性/White */
  //     background: #FFFFFF;
  //     box-sizing: border-box;
  //     /* Gray 中性/Gray4-边框 */
  //     border: 1px solid #DCDCDC;
  //     padding: 0 4px;
  //   }

  //   .semi-page-item-active {
  //     border-radius: 3px;
  //     background-color: #0052D9;
  //     color: #FFFFFF;
  //   }

  //   .semi-page-next,
  //   .semi-page-prev {
  //     border: 0px;
  //   }
  // }
}

/* 隐藏滚动条 */
.chatpptmain::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.fallbackMain {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fallbackImage {
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  color: #555;
}

.fallbackText {
  margin-top: 12px;
}

.recentConversation {
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: normal;
  height: 20px;
  line-height: 20px;
  letter-spacing: normal;
  color: #3D3D3D;
  margin: 12px 0;
}

.cardContent {
  width: 256px;
  height: 200px;
  border-radius: 8px;
}

.blackBg {
  // background: black;
  color: white;
  text-align: center;
  height: 140px;
  border-radius: 8px 8px 0 0;
}

.whiteBg {
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 10%);
  height: 60px;
  border-radius: 0 0 8px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.title {
  font-size: 14px;
  color: #000;
  font-weight: 500;
  line-height: 20px;
}

.time {
  font-size: 12px;
  color: #999;
  line-height: 20px;
}

.iconContainer {
  display: flex;
  justify-content: flex-end;
}

.card {
  width: auto;

  :global {
    .semi-card-body {
      padding: 0;
      margin: 0;
    }
  }
}

.cardmain {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(calc((100% - 32px) / 3), 1fr));
  gap: 16px;
}

.toolCard {
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-5px);
  }
}

.pagination {
  width: 100%;
  flex-basis: 100%;
  display: flex;
  justify-content: center;
  margin-top: 12px;
}
