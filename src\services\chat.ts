import { API_KEY } from '@/config';
import { jsonp } from '@/utils/jsonp';
import request from '@/utils/request';
import type {
  EnginItem,
  FeedBackInter,
  GuessYouWantParams,
  ImageTaskAbortParams,
  QueryChatListItem,
} from './types';

// 上传文件
export const uploadApi = '/infra/open/file/upload';

// 获取报告引用标准对话流接口
export const enginSendStreamApi = '/ai/engin/send-stream';

// 获取NewChatId
export const fetchNewChatIdApi = '/ai/chat/msg/getNewChatId';

// 获取对话列表
export const chatListApi = '/ai/chat/history/recentHistoryList';

// 获取对话列表
export const chatHistoryListApi = '/ai/chat/msg/getChatList';

// 知识库列表
export const knowledgeListApi = '/ai/knowledge/queryKnowledgeList';

// 文件解析
export const parseFileToMarkdownApi = '/ai/app/transferUrl';

// 猜你想问
export const guessYouWantApi = '/ai/chat/msg/createQuestionGuide';

// 反馈对话情况：点赞 拉踩
export const feedBackContentApi = '/ai/chat/msg/feedBackContentId';

// 更新对话标题
export const updateChatTitleApi = '/ai/chat/history/update-chatTitle';

// 删除对话记录
export const deleteChatByIdApi = '/ai/chat/msg/deleteDataId';

// 删除对话记录
export const deleteHistoryByIdApi = '/ai/chat/history/delete-chat';

// 智慧工程列表
export const queryEnginListApi = '/ai/engin/queryEnginList';

// 获取引用报告标准检测
export const completionChatApi = '/ai/report/reportStdCheck/completionChat';

// 消息来源文件预览
export const readFileApi = '/ai/chat/msg/readFile';

// 文生图对话接口
export const imageSendStreamApi = '/ai/image/send-stream';

// 文生图中断接口
export const imageTaskAbortApi = '/ai/image/taskInterruption';

// 腾讯地图天气接口
export const WEATHER_URL = 'https://apis.map.qq.com/ws/weather/v1';

// 腾讯地图定位接口
export const CITY_URL = `https://apis.map.qq.com/ws/location/v1/ip`;

/**
 * 根据来源获取对话流接口
 * @param params
 * @returns '/ai/chat/send-stream'; // 新对话接口
 */
/**
 * 获取对话流接口地址，支持灵活扩展
 * @param options { appCode: string }
 * @returns string
 */
const chatStreamApiMap: Record<string, string> = {
  report_compare: enginSendStreamApi,
  report_valid: completionChatApi,
  // 可在此处继续添加特殊 appCode 的映射
};

export function getChatStreamApiUrl({ appCode = 'chat' }: { appCode?: string }) {
  const path = chatStreamApiMap[appCode] || `/ai/${appCode}/send-stream`;
  return `${process.env.API_URL}${path}`;
}

/**
 * 获取NewChatId
 * appCode: 新对话 'chat'
 */
export async function fetchNewChatId(params: {
  appCode: string; // 应用Code
}) {
  return request(fetchNewChatIdApi, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params,
  });
}

/**
 * 解析文件
 * @param data FormData 对象，包含 url 字段
 * @returns Promise
 */
export async function parseFileToMarkdown(data: string) {
  return request<{
    markdown: string;
    markdown_url: string;
  }>(`${parseFileToMarkdownApi}?url=${encodeURIComponent(data)}`, {
    method: 'POST',
  });
}

/**
 *  更新对话标题
 * @param params
 * @returns Promise
 */
export async function updateChatTitle(data: { chatId: string; title: string }) {
  return request(updateChatTitleApi, {
    method: 'POST',
    params: data,
  });
}

/**
 *  查询对话记录列表
 * @param title
 * @returns Promise
 */
export async function queryChatList() {
  return request(chatListApi, {
    method: 'POST',
    // params: {}
  });
}

/**
 *  查询猜你想问
 * @param GuessYouWantParams
 * @returns Promise
 */
export async function guessYouWant(data: GuessYouWantParams) {
  return request(guessYouWantApi, {
    method: 'POST',
    data,
  });
}

/**
 *  删除对话记录
 * @param chatId
 * @returns Promise
 */
export async function deleteChatById(chatId: string) {
  return request(deleteHistoryByIdApi, {
    method: 'POST',
    params: { chatId },
  });
}

export async function getHistoryChatList(data: any) {
  return request<{
    data: any;
    list: QueryChatListItem[];
  }>(chatHistoryListApi, {
    method: 'POST',
    data,
  });
}

/**
 * 删除对话记录列表
 */
export const deleteDataId = (data: any) =>
  request(deleteChatByIdApi, {
    method: 'POST',
    data,
  });

/**
 * 反馈对话情况
 */
export const feedBackContentById = (data: FeedBackInter) =>
  request(feedBackContentApi, {
    method: 'POST',
    data,
  });

/**
 * 获取知识库列表
 * @param data
 * @returns
 */
export const getKnowledgeList = (data: any) =>
  request(knowledgeListApi, {
    method: 'GET',
    data,
  });

/**
 * 获取智慧工程
 * @param data
 * @returns
 */
export const getEnginList = () =>
  request<EnginItem[]>(queryEnginListApi, {
    method: 'GET',
  });

/**
 * 腾讯地图定位
 */
export const getCity = () =>
  jsonp(CITY_URL, { key: API_KEY, output: 'jsonp' }).then((res: any) =>
    res.status === 0 ? res.result.ad_info : res,
  );

/**
 * 腾讯地图获取当前城市天气
 */
export const getWeather = (adcode: number, extensions: string) =>
  jsonp(WEATHER_URL, { key: API_KEY, output: 'jsonp', adcode, type: extensions }).then((res: any) =>
    res.status === 0 ? res.result : res,
  );

/**
 * 获取首页轮播图
 */
export const getHomeSwiperList = () =>
  request<{
    imageList: {
      createTime: number;
      creator: null | string;
      deleted: boolean;
      id: number;
      title: string;
      updateTime: number;
      updater: null | string;
      url: string;
    }[];
    nowTime: number;
  }>('/ai/app/image', {
    method: 'GET',
  });

/**
 * 消息来源文件预览
 */
export const getPreviewOfFile = (collectionId: string) => {
  return request(`${readFileApi}?collectionId=${collectionId}`, {
    method: 'GET',
  });
};

/**
 * 文生图中断接口 中断任务
 */
export const imageTaskAbort = (params: ImageTaskAbortParams) => {
  return request(imageTaskAbortApi, {
    method: 'GET',
    params,
  });
};
