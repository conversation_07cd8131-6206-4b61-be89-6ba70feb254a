import { ConfigWith4A } from '@/config/4AConfig';
import { setAccessToken, setRefreshToken } from '@/utils';
import request from '@/utils/request';
import { Spin } from '@douyinfe/semi-ui';
import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import './index.less';

/**
 * 4A 登录
 */
const FourALogin: React.FC = () => {
  // 获取Router code
  const [searchParams] = useSearchParams();
  const code = searchParams.get('code');
  const fromPage = searchParams.get('fromPage');

  interface SSORES {
    accessToken: string;
    refreshToken: string;
  }
  // 获取token
  const sso4aLogin = () => {
    if (code) {
      request(`/system/auth/oauth2-4a`, {
        method: 'POST',
        params: {
          code,
          state: ConfigWith4A.clientId,
        },
      }).then((res) => {
        const data = res.data as unknown as SSORES;
        if (res && data) {
          setAccessToken(data.accessToken);
          setRefreshToken(data.refreshToken);
          window.location.href = `${window.location.origin}`;
        } else {
          window.location.href = ConfigWith4A.LogoutUrl();
        }
      });
    } else {
      window.location.href = ConfigWith4A.LogoutUrl();
    }
  };

  useEffect(() => {
    if (code) {
      sso4aLogin();
    }
  }, [code]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '90vh',
      }}
    >
      <Spin size="large" wrapperClassName="login-spin-wrapper-center" tip="登录中" />
    </div>
  );
};

export default FourALogin;
