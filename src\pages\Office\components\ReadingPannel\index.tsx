/**
 * AI阅读
 */
import { Card, Col, Row } from '@douyinfe/semi-ui';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, useSelector } from 'umi';
import styles from './index.less';

import pdfIcon from '@/assets/docicons/pdf.svg';
import pptIcon from '@/assets/docicons/ppt.svg';
import docIcon from '@/assets/docicons/word .svg';

import iconToGr from '@/assets/chatpdf/icon_rapp.svg';
import iconToList from '@/assets/chatpdf/icon_rbulletpoint.svg';

import request from '@/utils/request';

const ReadingPannel: React.FC = () => {
  const [docs, setDocs] = useState([]);
  const readMainRef = useRef<HTMLDivElement | null>(null);
  const semiChatInnerRef = useRef<Element | null>(null);

  const recentReadList = useSelector(
    (state: { recentAiRead: { list: '' } }) => state.recentAiRead.list,
  );
  const [showmode, setShowmode] = useState('gr');

  let navigate = useNavigate();

  function getDocItemIcon(item) {
    if (item.fileext == 'pdf') return pdfIcon;
    if (item.fileext == 'doc') return docIcon;
    if (item.fileext == 'docx') return docIcon;
    if (item.fileext == 'ppt') return pptIcon;
    if (item.fileext == 'pptx') return pptIcon;
    return '';
  }

  function getDocItemDescription(item) {
    //return [ formatTime(item.time, 'm-d H:i') , item.type.toUpperCase() , (item.wordcount ? (item.wordcount+'字'):'')  ].join(' · ') ;
    return [
      item.time,
      item.fileext.toUpperCase() /*, (item.wordcount ? (item.wordcount+'字'):'') */,
    ].join(' · ');
  }

  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);

  function refreshList() {
    let params1 = {
      page: 1,
      pagesize: 15,
    };

    request('/ai/read/userDocumentList', {
      method: 'POST',
      data: params1,
    }).then((ddata) => {
      let rslist = ddata.data?.list.map((it) => {
        let ait: any = {};
        ait.chatId = it.chatId;
        ait.images = [];
        ait.title = [];
        ait.time = '';
        ait.wordcount = 0;
        ait.docnum = 0;
        ait.fileext = '';

        it.documents.map((iit) => {
          ait.images.push(iit.docThumbnail);
          ait.title.push(iit.fileName);
          ait.wordcount += iit.docWordCount;
          ait.docnum++;

          if (!ait.time) ait.time = iit.updateTime;
          if (!ait.fileext) ait.fileext = iit.fileType;
        });

        ait.images.slice(0, 5);

        ait.docname = ait.title[0] + (ait.title.length > 1 ? '等' + ait.docnum + '篇文章' : '');
        ait.icon = ait.images[0];

        return ait;
      });

      setDocs(rslist);
    });
  }

  useEffect(() => {
    refreshList();
    // 获取 semi-chat-inner 元素
    const element = document.querySelector('.semi-chat-inner');
    semiChatInnerRef.current = element;
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const { height: semiChatInnerHeight } = entry.contentRect;
          const windowHeight = window.innerHeight;
          const containerHeight = windowHeight - 140 - semiChatInnerHeight;
          if (readMainRef.current) {
            readMainRef.current.style.height = `${containerHeight}px`;
            readMainRef.current.style.paddingBottom = '12px';
          }
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
    /*
    dispatchInUtils({
      type: 'recentAiRead/fetchList',
      payload: { },
    });
    */
    return () => {};
  }, []);

  function clickToChangeShowMode() {
    setShowmode(showmode == 'gr' ? 'list' : 'gr');
  }

  let udocs = docs;

  return (
    <div ref={readMainRef} className={styles.chatWidthDocWrap}>
      <div className={styles.chatWidthDocRecentT1}>
        <div className={styles.chatWidthDocRecentT1in}>最近阅读</div>

        <div onClick={clickToChangeShowMode} className={styles.chatWidthDocRecentT1inicon}>
          {showmode == 'gr' ? (
            <img src={iconToGr} className={styles.chatIconBtn} />
          ) : (
            <img src={iconToList} className={styles.chatIconBtn} />
          )}
        </div>
      </div>
      {udocs && udocs.length < 1 && (
        <div className={styles.chatNoRead}>最近暂无阅读,请上传pdf/word/ppt文档开始阅读</div>
      )}

      {showmode == 'gr' ? (
        <div className={styles.chatDocThumbWrap}>
          <Row gutter={[24, 24]}>
            {udocs &&
              udocs.map((item, index) => (
                <Col span={8} key={item.chatId}>
                  <Card
                    bodyStyle={{ padding: 0 }}
                    className={styles.chatWidtDocItem}
                    shadows="hover"
                    onClick={() => {
                      navigate(`/chat/${item.chatId}?appCode=read&pagemode=doc`);
                    }}
                    cover={
                      <div className={styles.chatWidtDocItemCover}>
                        {item.images.length > 1 ? (
                          <div
                            className={[
                              styles.chatWidtDocItemCoverIns,
                              styles['chatWidtDocItemCoverIns_' + item.images.length],
                            ].join(' ')}
                          >
                            {item.images.map((iimage, iiindex) => (
                              <img
                                src={iimage}
                                key={iiindex}
                                className={[
                                  styles.chatWidtDocItemCoverInsIcon,
                                  styles['chatWidtDocItemCoverInsIcon_' + iiindex],
                                ].join(' ')}
                              />
                            ))}
                          </div>
                        ) : (
                          <img src={item.icon} />
                        )}
                      </div>
                    }
                  >
                    <div className={styles.chatWidtDocItemDesc}>
                      <img className={styles.chatWidtDocItemDescIcon} src={getDocItemIcon(item)} />
                      <div className={styles.chatWidtDocItemDescIn}>
                        <div className={styles.chatWidtDocItemDescTit}>{item.docname}</div>
                        <div className={styles.chatWidtDocItemDescInf}>
                          {getDocItemDescription(item)}
                        </div>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
          </Row>
        </div>
      ) : (
        <div className={styles.chatDocListWrap}>
          {udocs &&
            udocs.map((item, index) => (
              <div
                className={styles.chatWidtDocItemDesc}
                key={item.chatId}
                onClick={() => {
                  navigate(`/chat/${item.chatId}?appCode=read&pagemode=doc`);
                }}
              >
                <div className={styles.chatWidtDocItemDescDiv} src={getDocItemIcon(item)}>
                  <img className={styles.chatWidtDocItemDescIcon} src={getDocItemIcon(item)} />
                  <div className={styles.chatWidtDocItemDescNum}>{item.docnum}</div>
                </div>

                <div className={styles.chatWidtDocItemDescIn}>
                  <div className={styles.chatWidtDocItemDescTit}>{item.docname}</div>
                  <div className={styles.chatWidtDocItemDescInf}>{getDocItemDescription(item)}</div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};
export default ReadingPannel;
