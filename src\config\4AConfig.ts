/**
 * 客户端id
 */
const CLIENT_ID = 'TJAI'; // 'f8b8bd0d77c343e99f5ce8995c81e41e';

const CurrentOrigin = process.env.VITE_GLOB_4A_URL as string;

/**
 * 4A 相关配置
 */
export const ConfigWith4A = {
  /**
   * 客户端id TJAI
   */
  clientId: CLIENT_ID,

  /**
   * 4A登录地址
   */
  authorizeUrl: `https://yrz.bhidi.com/idp/oauth2/authorize?client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(
    CurrentOrigin + '/4Alogin',
  )}&response_type=code`,

  /**
   * 4A 退出地址
   */
  LogoutUrl: (redict = CurrentOrigin) =>
    `https://yrz.bhidi.com/idp/profile/OAUTH2/Redirect/GLO?redirctToUrl=${redict}&redirectToLogin=true&entityId=${CLIENT_ID}`,
};
