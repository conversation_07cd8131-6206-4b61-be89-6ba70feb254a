/**
 * 智能Ppt
 */
import deleteFile from '@/assets/chat/delete.svg';
import download from '@/assets/chat/download.svg';
import fallbackImage from '@/assets/chat/fallbackImage.svg';
import { PPTItem } from '@/models/historyChat';
import stylePublic from '@/styles/officetemplate-common.less';
import { formatTimestamp } from '@/utils';
import { Button, Card, Pagination, Popconfirm, Spin, Typography } from '@douyinfe/semi-ui';
import React, { useEffect, useRef } from 'react';
import styles from './index.less';

interface ToolListProps {
  loading: boolean;
  tools: PPTItem[];
  onDownload: (item: any) => void;
  // 删除按钮的点击事件处理函数
  onDelete: (item: any) => void;
  // 列表项的点击事件处理函数
  onItemClick: (item: any) => void;
  // 父组件传递的当前页码
  page: number;
  // 父组件传递的页码改变回调函数
  onPageChange: (page: number) => void;
  // 父组件传递的每页数量
  pageSize: number;
  // 父组件传递的总数量
  total: number;
}

const ChatPpt: React.FC<ToolListProps> = ({
  loading,
  tools,
  onDownload,
  onDelete,
  onItemClick,
  page,
  onPageChange,
  pageSize,
  total,
}) => {
  const { Text } = Typography;
  const containerRef = useRef<HTMLDivElement | null>(null);
  const fallbackMain = useRef<HTMLDivElement | null>(null);
  const semiChatInnerRef = useRef<Element | null>(null);

  const handlePageChange = (cPage: number) => {
    // 阻止事件冒泡
    // 这里 event 未定义，需要修改
    event.stopPropagation();
    onPageChange(cPage);
  };

  useEffect(() => {
    // 获取 semi-chat-inner 元素
    const element = document.querySelector('.semi-chat-inner');
    semiChatInnerRef.current = element;
    if (element) {
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const { height: semiChatInnerHeight } = entry.contentRect;
          const windowHeight = window.innerHeight;
          const containerHeight = windowHeight - 140 - semiChatInnerHeight;
          if (containerRef.current) {
            containerRef.current.style.height = `${containerHeight}px`;
            containerRef.current.style.paddingBottom = '12px';
          }
          if (fallbackMain.current) {
            fallbackMain.current.style.height = `${containerHeight}px`;
          }
        });
      });
      resizeObserver.observe(element);
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [page, tools]);

  if (loading) {
    return (
      <div className={stylePublic.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  if (tools.length === 0) {
    return (
      <div ref={fallbackMain} className={styles.fallbackMain}>
        <div className={styles.fallbackImage}>
          <img src={fallbackImage} alt="" />
          <div className={styles.fallbackText}>您还没有作品，快去智能生成</div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={styles.chatpptmain}>
      <div className={styles.recentConversation}>最近会话</div>
      <div className={styles.cardmain}>
        {tools &&
          tools.map((item) => {
            // 使用 item 中的唯一标识作为 key
            return (
              <Card key={item.id} shadows="hover" className={`${styles.card} ${styles.toolCard}`}>
                <div className={styles.cardContent}>
                  {/* 黑色背景区域，展示大标题 */}
                  <div className={styles.blackBg} onClick={() => onItemClick(item)}>
                    <img src={item.thumbnail} alt="" />
                  </div>
                  {/* 白色背景区域，展示标题和时间等信息 */}
                  <div className={styles.whiteBg}>
                    <div>
                      {/* <div className={styles.title}>{item.title}</div> */}
                      <Text
                        ellipsis={{
                          rows: 1,
                          showTooltip: { opts: { style: { width: 162 } } },
                        }}
                        style={{ width: 180 }}
                        className={styles.title}
                      >
                        {item.title}
                      </Text>
                      <div className={styles.time}>{formatTimestamp(item.updateTime)}</div>
                    </div>
                    {/* 这里可以添加删除和下载图标相关逻辑 */}
                    <div className={styles.iconContainer}>
                      <Button
                        className="!p-0 !w-[24px] !h-[24px]"
                        theme="borderless"
                        icon={
                          <img
                            src={download}
                            onClick={(e) => {
                              e.stopPropagation();
                              onDownload(item);
                            }}
                          />
                        }
                      />
                      <Popconfirm
                        title="确定要删除此条记录吗？"
                        content="删除后，内容无法恢复。"
                        onConfirm={() => onDelete(item)}
                      >
                        <Button
                          className="!p-0 !w-[24px] !h-[24px]"
                          theme="borderless"
                          icon={<img src={deleteFile} />}
                        />
                      </Popconfirm>
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
      </div>
      {/* 当数据数量大于每页数量时才展示分页器 */}
      {total > pageSize && (
        <Pagination
          className={styles.pagination}
          pageSize={pageSize}
          total={total}
          currentPage={page}
          onChange={(cPage) => handlePageChange(cPage)}
        />
      )}
    </div>
  );
};

export default ChatPpt;
