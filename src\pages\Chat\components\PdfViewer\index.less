.pdfViewer {
  flex: 1;
  box-sizing: border-box;
  padding-right: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .pdfViewerToolsBar {
    height: 53px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    position: relative;
    z-index: 100;

    &.isRoute {
      padding: 0 20px;
    }

    .btnThumbControl {
      padding-right: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }

    .title {
      flex: 1;
    }

    .btnToBig {
      padding-left: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }

    .btnClose {
      padding-left: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }
  }

  .pdfIframe {
    flex: 1;
    height: 100%;
  }
}
