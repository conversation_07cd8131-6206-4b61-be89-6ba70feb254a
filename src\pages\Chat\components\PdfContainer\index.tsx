import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'umi';

import { IconLoading } from '@douyinfe/semi-icons';
import { Button, Spin, Toast } from '@douyinfe/semi-ui';

import { Document, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

import styles from './index.less';

import PPage from './PPage';
import SelectToolsBar from './SelectToolsBar';
import TopBar from './TopBar';

import { AuthModelState } from '@/models/auth';
import { dispatchInUtils, getState } from '@/utils';
import request from '@/utils/request';
import classNames from 'classnames';

const pdfOptions = {
  cMapUrl: `/pdfjs-dist/web/cmaps/`,
};

pdfjs.GlobalWorkerOptions.workerSrc = `/pdfjs-dist/build/pdf.worker.min.mjs`;

interface Props {
  chatId?: string;
  appCode: string;
}

const PdfContainer: React.FC<Props> = (props: Props) => {
  const url = useSelector((state: { pdfContainer: { url: '' } }) => state.pdfContainer.url);
  const pdfFileName = useSelector(
    (state: { pdfContainer: { name: '' } }) => state.pdfContainer.name,
  );
  const pdfFileSize = useSelector(
    (state: { pdfContainer: { size: '' } }) => state.pdfContainer.size,
  );

  const [pwidth, setPwidth] = useState(0);
  const [pages, setPages] = useState([]);

  const [oxdatas, setOxdatas] = useState([]);
  const [transdatas, setTransdatas] = useState([]);

  const [dinViewPage, setDinViewPage] = useState([]);

  const [uUrl, setUUrl] = useState('');

  const [pageLoadingTxt, setPageLoadingTxt] = useState('文档解析中...');
  const [pageLoadingId, setPageLoadingId] = useState('loading');

  const transRefdatas = useRef(null);
  const dooTransTaskingRef = useRef(null);

  const tUrlRef = useRef(null);
  const oxdatasRef = useRef(null);

  const [pagesum, setPagesum] = useState('');
  const [pagenum, setPagenum] = useState('');

  const [showOriPdf, setShowOriPdf] = useState(false);
  const [showSelectToolsBar, setShowSelectToolsBar] = useState(false);
  const [selectToolsBarLeft, setSelectToolsBarLeft] = useState(0);
  const [selectToolsBarTop, setSelectToolsBarTop] = useState(0);
  const [selectToolsBarMode, setSelectToolsBarMode] = useState('');

  const [selectToolsBarLangSelectorVisible, setSelectToolsBarLangSelectorVisible] = useState(false);
  const [selectContent, setSelectContent] = useState('');

  const [sourceLang, setSourceLang] = useState({ id: 'auto', name: '自动识别', enname: '' });
  const [targetLang, setTargetLang] = useState({ id: 'en', name: 'English', enname: 'English' });

  const pwidthTimer = useRef(null);
  const transRequests = useRef(null);
  const wrapRef = useRef(null);

  const containerInRef = useRef(null);

  const ppagesRef = useRef(null);

  const docInfoRef = useRef(null);
  const wordcountRef = useRef(null);

  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);

  //是否是翻译模式
  const [transMode, setTransMode] = useState(false);

  //请求oxdata队列
  const fetchOxDataPusdosRef = useRef(null);
  const fetchOxDataPusdoing = useRef(null);

  useEffect(() => {
    setOxdatas([]);
    setTransdatas([]);
    setTransMode(false);
    dispatchInUtils({
      type: 'pageLayout/changeTransState',
      payload: {
        showtrans: false,
        transhowori: getState().pageLayout.transhowori,
      },
    });
    console.log(1, url);

    transRefdatas.current = [];
    transRequests.current = [];

    fetchOxDataPusdosRef.current = [];
    fetchOxDataPusdoing.current = false;

    docInfoRef.current = {};
    wordcountRef.current = null;
    oxdatasRef.current = null;

    dooTransTaskingRef.current = false;

    appCanvasRef.current = null;

    tUrlRef.current = url;

    if (dinScrollTimerRef.current) clearTimeout(dinScrollTimerRef.current);
    dinViewPageRef.current = null;
    setDinViewPage(null);
    setPagesum('');
    setPagenum('1');

    setUUrl('');

    if (url) {
      startFetchNewDoc(url);
    }
  }, [url]);

  const startOxDataTimerRef = useRef(null);
  useEffect(() => {
    if (!dinViewPage || !dinViewPage.page) return;
    if (startOxDataTimerRef.current) clearTimeout(startOxDataTimerRef.current);

    startOxDataTimerRef.current = setTimeout(() => {
      startFetchOxData(dinViewPage.page);
    }, 500);
  }, [dinViewPage]);

  function startFetchNewDoc(aurl: string) {
    const exttmps = aurl.split('?')[0].split('.');
    const fileExt = exttmps[exttmps.length - 1];
    const params = {
      fileUrl: aurl,
      userId: user?.id + '',
      chatId: props.chatId,
      fileName: pdfFileName,
      fileSize: pdfFileSize ? pdfFileSize : '-',
      fileType: fileExt,
    };

    setPageLoadingId('loading');
    setPageLoadingTxt('文档加载中...');

    request('/ai/read/parseDocumentInfo', {
      method: 'POST',
      data: params,
    })
      .then((ddata) => {
        if (!ddata || !ddata.data || !ddata.data.pdfUrl || !ddata.data.pdfPageSize) {
          setPageLoadingTxt('文档加载失败');
          setPageLoadingId('error');
          return;
        }

        //对比是否是当前文档
        if (aurl !== tUrlRef.current) return;

        setPageLoadingId('');
        setUUrl(ddata.data.pdfUrl);
        setPagesum(ddata.data.pdfPageSize.length);

        docInfoRef.current = ddata.data;
      })
      .catch(() => {
        setPageLoadingTxt('文档加载失败');
        setPageLoadingId('error');
      });
  }

  function startFetchOxData(num: number) {
    if (!docInfoRef.current || !docInfoRef.current.pdfPageSize) return;
    if (!fetchOxDataPusdosRef.current) fetchOxDataPusdosRef.current = [];

    const pageSize = 5;
    let pageNum = Math.ceil(num / pageSize);
    let startNum = (pageNum - 1) * pageSize + 1;
    let endNum = startNum + pageSize - 1;
    if (endNum > docInfoRef.current.pdfPageSize.length)
      endNum = docInfoRef.current.pdfPageSize.length;

    //已经存在数据，则不再请求
    if (oxdatasRef.current) {
      let rightCount = 0;
      let dCount = 0;
      for (let i = startNum; i <= endNum; i++) {
        dCount++;
        if (oxdatasRef.current.find((it) => it.page == startNum)) rightCount++;
      }

      if (rightCount >= dCount) return;
    }

    fetchOxDataPusdosRef.current.push({
      pageNum,
      pageSize,
      startNum,
      endNum,
      range: startNum + '-' + endNum,
    });

    dooFetchOxData();
  }

  function dooFetchOxData() {
    if (fetchOxDataPusdoing.current) return;
    if (!docInfoRef.current) return;

    if (!fetchOxDataPusdosRef.current || fetchOxDataPusdosRef.current.length < 1) return;

    let adoo = fetchOxDataPusdosRef.current.pop();

    const params = {
      docId: docInfoRef.current.docId,
      pdfUrl: docInfoRef.current.pdfUrl,
      docPageRange: adoo.range,
    };

    fetchOxDataPusdoing.current = true;
    request('/ai/read/parseDocumentJson', {
      method: 'POST',
      data: params,
    })
      .then((ddata) => {
        fetchOxDataPusdoing.current = false;
        if (!docInfoRef.current) return;

        if (!ddata || !ddata.data) {
          dooFetchOxData();
          return;
        }

        let xdata = ddata.data;
        if (xdata.docId !== docInfoRef.current.docId) {
          dooFetchOxData();
          return;
        }

        if (!oxdatasRef.current) oxdatasRef.current = [];
        xdata.docJson.forEach((it) => {
          let itdatas = [];
          it.datas.forEach((iit) => {
            if (iit.type === 'image') return;
            if (iit.txt.trim() === '') return;

            itdatas.push(iit);
          });

          oxdatasRef.current.push({
            page: adoo.startNum - 1 + it.page,
            datas: itdatas,
          });
        });
        setOxdatas(oxdatasRef.current.map((it) => it));
        dooFetchOxData();

        oxdatasRef.current.forEach((it) => {
          let ats = transTasks.current.find((iit) => iit.apage.page === it.page && iit.undata);
          if (!ats) return;

          ats.undata = false;
          dooTransTask();
        });
      })
      .catch(() => {
        fetchOxDataPusdoing.current = false;
        dooFetchOxData();
      });
  }

  function getOxdata(apage: any) {
    const xpage = apage?.page;

    let adata = oxdatas.find((it) => it.page === xpage);
    if (adata) return adata;

    return {
      loading: true,
    };
  }

  function getPpagesRefMap() {
    if (!ppagesRef.current) {
      ppagesRef.current = new Map();
    }

    return ppagesRef.current;
  }

  function onTopBarBtnClick(ddata: any) {
    if (ddata.action === 'showOriPdfChange') {
      dispatchInUtils({
        type: 'pageLayout/changeTransState',
        payload: {
          showtrans: transMode,
          transhowori: !showOriPdf,
        },
      });

      setShowOriPdf(!showOriPdf);
      return;
    }

    if (ddata.action === 'changeTransMode') {
      changeTransMode();
    }
  }

  function changeTransMode() {
    if (!transMode) {
      if (!docInfoRef.current || !docInfoRef.current.docId) {
        Toast.info('文档解析中，请稍后再试');
        return;
      }

      startTrans();
    }

    dispatchInUtils({
      type: 'pageLayout/changeTransState',
      payload: {
        showtrans: !transMode,
        transhowori: showOriPdf,
      },
    });

    setTransMode(!transMode);

    setShowSelectToolsBar(false);
  }

  function onDocumentLoadSuccess(document) {
    let { numPages } = document;
    //if(numPages > 10) numPages = 10 ;

    let ppages = docInfoRef.current.pdfPageSize.map((it) => {
      return {
        page: it.page,
        width: it.width,
        height: it.height,
      };
    });

    setPages(ppages);
  }
  const pwidthRef = useRef(0);
  function dsetPwidth(w: number) {
    let rw = w * 0.8;
    if (showOriPdf && transMode) {
      rw = w * 0.95;
      if (rw * 0.05 < 10) rw = (w - 10) * 0.95;
    }
    if (rw > 800) rw = 800;
    pwidthRef.current = rw;
    setPwidth(rw);
  }

  const resizeObserver = new ResizeObserver((entries) => {
    if (pwidthTimer.current) clearTimeout(pwidthTimer.current);

    let tpwidth = entries[0].contentRect.width;
    if (showOriPdf && transMode) tpwidth = tpwidth / 2;

    if (Math.abs(pwidthRef.current - tpwidth) < 10) return;

    pwidthTimer.current = setTimeout(() => {
      dsetPwidth(tpwidth);
    }, 500);
  });

  function onPdfContainerRef(d) {
    if (d) {
      if (!pwidth) dsetPwidth(d.offsetWidth);
      resizeObserver.observe(d);
    }

    containerInRef.current = d;
  }

  const transTasks = useRef(null);
  function startTrans() {
    if (!transTasks.current) transTasks.current = [];
    if (!transRefdatas.current) transRefdatas.current = [];

    let currentpage = 1;
    if (dinViewPageRef.current) currentpage = dinViewPageRef.current.page;

    let tastCount = 0;

    pages.forEach((it: { page: number }) => {
      if (it.page === currentpage || it.page === currentpage + 1) {
        if (pushTransTask(it)) {
          tastCount++;
        }
      }
    });

    if (tastCount > 0) {
      setTransdatas(transRefdatas.current.map((it) => it));
      dooTransTask();
    }
  }

  function pushTransTask(it) {
    let transOxk = `x_${it.page}_${sourceLang.id}_${targetLang.id}`;

    let ind1 = transRefdatas.current.findIndex((iit) => iit.oxk == transOxk && iit.error);
    if (ind1 > -1) transRefdatas.current.splice(ind1);

    ind1 = transRefdatas.current.findIndex((iit) => iit.oxk == transOxk);
    if (ind1 > -1) return false;

    transTasks.current.push({
      url: tUrlRef.current + '',
      apage: it,
      sourceLang: JSON.parse(JSON.stringify(sourceLang)),
      targetLang: JSON.parse(JSON.stringify(targetLang)),
    });

    transRefdatas.current.push({
      page: it.page,
      oxk: transOxk,
      loading: true,
      sourceLangId: sourceLang.id,
      targetLangId: targetLang.id,
    });

    return true;
  }

  function retryTrans(apage) {
    let tastCount = 0;
    var apage = pages.find((it) => it.page == apage.page);
    if (apage) pushTransTask(apage) && tastCount++;

    if (tastCount > 0) {
      setTransdatas(transRefdatas.current.map((it) => it));
      dooTransTask();
    }
  }

  function dooTransTask() {
    if (dooTransTaskingRef.current) return;
    if (!transTasks.current || transTasks.current.length < 1) return;

    let ind = transTasks.current.findIndex((it) => !it.undata);
    if (ind < 0) return;

    let atask = transTasks.current[0];
    let aoxdatas =
      oxdatasRef.current && oxdatasRef.current.find((it) => it.page == atask.apage.page);

    if (!aoxdatas) {
      if (!atask.undata) {
        atask.undata = true;
      }

      ind = transTasks.current.findIndex((it) => !it.undata);
      if (ind > -1) {
        atask = transTasks.current[ind];
        transTasks.current.splice(ind, 1);
      }

      if (!atask) return dooTransTask();
    } else {
      transTasks.current.shift();
    }

    if (atask.url !== tUrlRef.current) return dooTransTask();

    const apage = atask.apage;
    const sourceLang1 = atask.sourceLang;
    const targetLang1 = atask.targetLang;

    let transOxk = `x_${apage.page}_${sourceLang1.id}_${targetLang1.id}`;

    let transData = transRefdatas.current.find((it) => it.oxk == transOxk);
    if (transData && !transData.loading && !transData.error) {
      dooTransTask();
      return;
    }

    //暂未解析，等解析后自动启动
    if (!aoxdatas || aoxdatas.loading) {
      atask.undata = true;
      transTasks.current.unshift(atask);

      dooTransTask();
      return;
    }

    let contents = [];
    aoxdatas &&
      aoxdatas.datas &&
      aoxdatas.datas.map((it) => {
        contents.push(it.txt.trim());
      });

    if (contents.length < 1) {
      dooTransTask();
      return null;
    }

    let params = {
      sourceLanguage: sourceLang1.id === 'auto' ? '' : sourceLang1.name,
      targetLanguage: targetLang1.id === 'auto' ? '' : targetLang1.name,
      contents,
    };

    if (transRequests.current && transRequests.current.find((it) => it.oxk == transOxk))
      return null;
    if (!transRequests.current) transRequests.current = [];
    transRequests.current.push({
      apage,
      oxk: transOxk,
    });

    dooTransTaskingRef.current = true;
    request('/ai/read/queryTranslate', {
      method: 'POST',
      data: params,
    })
      .then((ddata) => {
        dooTransTaskingRef.current = false;

        let rInd = transRequests.current.findIndex((it) => it.oxk == transOxk);
        if (rInd > -1) transRequests.current.splice(rInd, 1);

        let ind1 = transRefdatas.current.findIndex((it) => it.oxk == transOxk);
        if (ind1 > -1) transRefdatas.current.splice(ind1, 1);

        if (atask.url !== tUrlRef.current) return dooTransTask();

        let transData1 = {
          page: apage.page,
          datas: [],
          oxk: transOxk,
          sourceLangId: sourceLang1.id,
          targetLangId: targetLang1.id,
        };

        if (
          !ddata ||
          ddata.code !== 0 ||
          !ddata.data ||
          !ddata.data[0] ||
          !ddata.data[0].contents ||
          ddata.data[0].contents.length < 1
        ) {
          transData1.error = true;
          transRefdatas.current.push(transData1);

          setTransdatas(transRefdatas.current.map((it) => it));
          dooTransTask();
          return;
        }

        let mdata = ddata.data[0];
        mdata.contents.map((it, ind) => {
          transData1.datas.push({
            txt: it,
          });
        });

        transRefdatas.current.push(transData1);

        setTransdatas(transRefdatas.current.map((it) => it));
        dooTransTask();
      })
      .catch(() => {
        dooTransTaskingRef.current = false;
      });

    return;
  }

  function onTransOxItemClick(e, apage, oxitem, oxItemRef, transTxt) {
    let d1 = wrapRef.current.getClientRects();
    let d1in = oxItemRef.getClientRects();
    if (d1[0] && d1in[0]) {
      const barwidth = 203;
      let xleft = d1in[0].left - d1[0].left + d1in[0].width / 2 - barwidth / 2;
      let xtop = d1in[0].top - d1[0].top + d1in[0].height - 2;

      setShowSelectToolsBar(true);
      setSelectToolsBarLeft(xleft);
      setSelectToolsBarTop(xtop);
      setSelectToolsBarMode('trans');

      setSelectContent(transTxt);
    }

    const map = getPpagesRefMap();
    let ref = map.get(apage);
    if (!ref) return;

    ref.lightOxItem(oxitem);
  }

  function onPPageOxItemHover(e, apage, oxitem, oxItemRef) {
    if (!e) {
      setShowSelectToolsBar(false);
      setSelectContent('');
      return;
    }
    if (!wrapRef.current) return;

    let d1 = wrapRef.current.getClientRects();
    let d1in = oxItemRef.getClientRects();
    if (!d1[0] || !d1in[0]) return;

    const barwidth = 203;
    let xleft = d1in[0].left - d1[0].left + d1in[0].width / 2 - barwidth / 2;
    let xtop = d1in[0].top - d1[0].top + d1in[0].height - 2;

    setShowSelectToolsBar(true);
    setSelectToolsBarLeft(xleft);
    setSelectToolsBarTop(xtop);
    setSelectToolsBarMode('');

    setSelectContent(oxitem.txt);
  }

  function onPPageTextSelected(e, content) {
    if (!wrapRef.current) return;

    let d1 = wrapRef.current.getClientRects();
    if (!d1[0]) return;

    let xleft = e.pageX - d1[0].left - 50;
    let xtop = e.pageY - d1[0].top + 15;

    setShowSelectToolsBar(true);
    setSelectToolsBarLeft(xleft);
    setSelectToolsBarTop(xtop);
    setSelectToolsBarMode('');

    setSelectContent(content);
  }

  function onPPageMouseDown(e) {
    setShowSelectToolsBar(false);
    setSelectContent('');
    setSelectToolsBarLangSelectorVisible(false);
  }

  function onLangsChange(ddata) {
    if (ddata.type === 'target') setTargetLang(ddata.data);
    else if (ddata.type === 'source') setSourceLang(ddata.data);
  }

  function onSelectToolsBarLangSelectorShow() {
    setSelectToolsBarLangSelectorVisible(true);
  }

  function onSelectToolsBarLangSelectorHide() {
    setSelectToolsBarLangSelectorVisible(false);
  }

  const appCanvasRef = useRef(null);

  function onPageRenderSucc(aaapage, e) {
    if (!dinViewPageRef.current) {
      dinViewPageRef.current = pages[0];
      setDinViewPage(pages[0]);
      setPagenum('1');
    }
  }

  const dinScrollTimerRef = useRef(null);
  const dinScrollTimerTRef = useRef(null);
  const dinViewPageRef = useRef(null);
  function handleDInScroll(e) {
    if (dinScrollTimerRef.current) clearTimeout(dinScrollTimerRef.current);
    if (dinScrollTimerTRef.current) clearTimeout(dinScrollTimerTRef.current);
    dinViewPageRef.current = null;

    dinScrollTimerRef.current = setTimeout(() => {
      if (!docInfoRef.current || !docInfoRef.current.pdfPageSize) return;
      let sstop = e.target.scrollTop;
      let sheght = 0;

      let ppdfinfs = docInfoRef.current.pdfPageSize;

      for (let i = 0; i < ppdfinfs.length; i++) {
        let aapinf = ppdfinfs[i];

        let rate = aapinf.height / aapinf.width;
        let apheight = rate * pwidth + 10;

        if (sstop >= sheght && sstop <= sheght + apheight) {
          if (sstop >= sheght + (1 / 3) * apheight && ppdfinfs[i + 1]) {
            aapinf = ppdfinfs[i + 1];
          }

          dinViewPageRef.current = aapinf;
          setDinViewPage(aapinf);
          setPagenum(aapinf.page);

          break;
        }

        sheght += apheight;
      }
    }, 50);

    dinScrollTimerTRef.current = setTimeout(() => {
      if (!dinViewPageRef.current) return;

      let aapinf = dinViewPageRef.current;

      if (transMode) {
        let tastCount = 0;
        let apage = pages.find((it) => it.page == aapinf.page);
        if (apage) pushTransTask(apage) && tastCount++;

        apage = pages.find((it) => it.page == aapinf.page + 1);
        if (apage) pushTransTask(apage) && tastCount++;

        if (tastCount > 0) {
          setTransdatas(transRefdatas.current.map((it) => it));
          dooTransTask();
        }
      }
    }, 800);
  }

  const wheelTimerRef = useRef(null);
  function handleWheel() {
    setShowSelectToolsBar(false);

    if (wheelTimerRef.current) clearTimeout(wheelTimerRef.current);
    wheelTimerRef.current = setTimeout(() => {
      let ppagesRefMap = getPpagesRefMap();
      for (const appage of ppagesRefMap.values()) {
        appage && appage.lightOxItem(null);
      }
    }, 1000);
  }

  function dooRetry() {
    startFetchNewDoc(url);
  }

  function onPagerInputEnter() {
    scrollToPdfPage(pagenum);
  }

  function onPagerInputChange(num) {
    if (!pagesum) return;
    if (num * 1 < 1) {
      setPagenum('1');
      return;
    }
    if (num * 1 > pagesum * 1) {
      setPagenum(pagesum);
      return;
    }
    setPagenum(num);
  }

  function scrollToPdfPage(pnum) {
    if (!containerInRef.current) return;
    if (!docInfoRef.current || !docInfoRef.current.pdfPageSize) return;
    let ppdfinfs = docInfoRef.current.pdfPageSize;

    let sheight = 0;
    for (let i = 0; i < ppdfinfs.length; i++) {
      if (pnum - 1 === i) break;

      let aapinf = ppdfinfs[i];
      sheight += (aapinf.height / aapinf.width) * pwidth + 10;
    }

    console.log('==sheight=', sheight);

    containerInRef.current.scrollTo({
      top: sheight,
    });
  }

  const utrans =
    ['engin', 'report_compare', 'report_valid'].indexOf(props.appCode) > -1 ? false : true;

  if (pageLoadingId === 'loading') {
    return (
      <div className={styles.pdfContainerWrap} ref={(ref) => (wrapRef.current = ref)}>
        <TopBar
          title={pdfFileName}
          showOriPdf={showOriPdf}
          transMode={transMode}
          onBtnClick={onTopBarBtnClick}
          onLangsChange={onLangsChange}
          sourceLang={sourceLang}
          targetLang={targetLang}
          utrans={utrans}
          pagenum={pagenum}
          pagesum={pagesum}
        />
        <div className={styles.pageDocLoading}>
          <Spin indicator={<IconLoading />} />
          <div className={styles.pageDocLoadingTxt}>{pageLoadingTxt}</div>
        </div>
      </div>
    );
  }

  if (pageLoadingId === 'error') {
    return (
      <div className={styles.pdfContainerWrap} ref={(ref) => (wrapRef.current = ref)}>
        <TopBar
          title={pdfFileName}
          showOriPdf={showOriPdf}
          transMode={transMode}
          onBtnClick={onTopBarBtnClick}
          onLangsChange={onLangsChange}
          sourceLang={sourceLang}
          targetLang={targetLang}
          utrans={utrans}
          pagenum={pagenum}
          pagesum={pagesum}
        />
        <div className={styles.pageDocLoading}>
          <div className={styles.pageDocLoadingError}>
            {pageLoadingTxt}
            <div className={styles.pageDocLoadingErrorn}>
              <Button theme="outline" type="primary" onClick={dooRetry}>
                点击重试
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  //  if (!uUrl) return '';

  return (
    <div
      className={styles.pdfContainerWrap}
      ref={(ref) => (wrapRef.current = ref)}
      onWheel={handleWheel}
    >
      <TopBar
        title={pdfFileName}
        showOriPdf={showOriPdf}
        transMode={transMode}
        onBtnClick={onTopBarBtnClick}
        onLangsChange={onLangsChange}
        sourceLang={sourceLang}
        targetLang={targetLang}
        utrans={utrans}
        pagenum={pagenum}
        pagesum={pagesum}
        onPagerInputChange={onPagerInputChange}
        onPagerInputEnter={onPagerInputEnter}
      />

      <Document
        file={uUrl}
        options={pdfOptions}
        className={styles.pdfContainerDoc}
        onLoadSuccess={onDocumentLoadSuccess}
      >
        <div
          className={classNames([
            styles.pdfContainerIn,
            { [styles.pdfContainerW]: transMode && showOriPdf },
          ])}
          ref={onPdfContainerRef}
          onScroll={handleDInScroll}
        >
          <div className={styles.pdfContainerInX}>
            {(!transMode || showOriPdf) &&
              pages &&
              pages.map((apage) => {
                return (
                  <div className={styles.ppageWraap} key={apage.page}>
                    <PPage
                      onPageRenderSucc={onPageRenderSucc}
                      filename={pdfFileName}
                      fileurl={url}
                      chatid={props.chatId}
                      onOxItemHover={onPPageOxItemHover}
                      onTextSelected={onPPageTextSelected}
                      onPMouseDown={onPPageMouseDown}
                      ref={(node) => {
                        const map = getPpagesRefMap();
                        map.set(apage, node);
                      }}
                      currentPageNum={dinViewPage ? dinViewPage.page : 1}
                      hoverFreeze={selectToolsBarLangSelectorVisible}
                      apage={apage}
                      pwidth={pwidth}
                      oxdata={getOxdata(apage)}
                    />
                  </div>
                );
              })}
          </div>

          {transMode ? (
            <div className={[styles.pdfContainerInX, styles.pdfContainerInX1].join(' ')}>
              {pages &&
                pages.map((apage) => {
                  return (
                    <div key={apage.page} className={styles.ppageWraap}>
                      <PPage
                        onPMouseDown={onPPageMouseDown}
                        filename={pdfFileName}
                        fileurl={url}
                        chatid={props.chatId}
                        apage={apage}
                        pwidth={pwidth}
                        oxdata={getOxdata(apage)}
                        istransdoc={true}
                        retryTrans={retryTrans}
                        currentPageNum={dinViewPage ? dinViewPage.page : 1}
                        transdatas={transdatas.filter(
                          (it) =>
                            it.sourceLangId == sourceLang.id && it.targetLangId == targetLang.id,
                        )}
                        onTransOxItemClick={onTransOxItemClick}
                      />
                    </div>
                  );
                })}
            </div>
          ) : (
            ''
          )}
        </div>
      </Document>

      {showSelectToolsBar ? (
        <SelectToolsBar
          top={selectToolsBarTop}
          left={selectToolsBarLeft}
          mode={selectToolsBarMode}
          onLangSelectorShow={onSelectToolsBarLangSelectorShow}
          onLangSelectorHide={onSelectToolsBarLangSelectorHide}
          content={selectContent}
          chatid={props.chatId}
          utrans={utrans}
        ></SelectToolsBar>
      ) : (
        ''
      )}
    </div>
  );
};

export default PdfContainer;
