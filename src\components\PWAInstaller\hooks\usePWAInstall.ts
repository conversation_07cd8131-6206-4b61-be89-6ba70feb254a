// src/hooks/usePWAInstall.ts
import { useCallback, useEffect, useState } from 'react';

interface UsePWAInstallResult {
  canInstall: boolean;
  isInstalled: boolean;
  installApp: () => Promise<boolean>;
  error: Error | null;
}

export const usePWAInstall = (): UsePWAInstallResult => {
  const [deferredPrompt, setDeferredPrompt] = useState<any | null>(null);
  const [canInstall, setCanInstall] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // 检测是否已安装为 PWA
    const isInStandaloneMode = () =>
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true;

    setIsInstalled(isInStandaloneMode());

    // 监听 PWA 安装提示事件
    const handleBeforeInstallPrompt = (e: any) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setCanInstall(true);
    };

    // 监听应用安装成功事件
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setCanInstall(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as EventListener);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const installApp = useCallback(async (): Promise<boolean> => {
    if (!deferredPrompt) {
      setError(new Error('安装条件不满足，请稍后再试！'));
      return false;
    }

    try {
      // 显示安装提示
      deferredPrompt.prompt();

      // 等待用户选择
      const choiceResult = await deferredPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('用户已安装应用');
        return true;
      } else {
        console.log('用户拒绝安装');
        setError(new Error('用户拒绝安装应用'));
        return false;
      }
    } catch (err) {
      console.error('安装过程出错:', err);
      setError(err as Error);
      return false;
    } finally {
      setDeferredPrompt(null);
      setCanInstall(false);
    }
  }, [deferredPrompt]);

  return {
    canInstall,
    isInstalled,
    installApp,
    error,
  };
};
