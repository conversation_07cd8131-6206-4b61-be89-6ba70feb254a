import enlarge from '@/assets/chat/enlarge.svg';
import opentemplate from '@/assets/chat/opentemplate.svg';
import pen from '@/assets/chat/pen.svg';
import { NO_TEMPLATE_APPS } from '@/config/preferences';
import TemplateContent from '@/pages/Office/components/TemplateContent';
import styles from '@/styles/officetemplate-common.less';
import { Button, Popover, Typography } from '@douyinfe/semi-ui';
import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { useSearchParams } from 'react-router-dom';
interface TemplateHeaderProps {
  title?: string;
  currNav: string;
  onSelectTemplate?: (template: any) => void;
  onClose?: () => void;
  changeNav?: (nav: string) => void;
  showTemplate: boolean;
  setShowTemplate: React.Dispatch<React.SetStateAction<boolean>>;
}

interface HandleToggle {
  toggleTemplate: () => void;
}

const TemplateHeader = forwardRef<HandleToggle, TemplateHeaderProps>(
  (
    { onSelectTemplate, onClose, title, currNav, changeNav, showTemplate, setShowTemplate },
    ref,
  ) => {
    // 使用 useImperativeHandle 暴露方法
    useImperativeHandle(ref, () => ({
      toggleTemplate: () => setShowTemplate(false),
    }));

    const [searchParams, setSearchParams] = useSearchParams();
    const appCode = searchParams.get('appCode') || '';

    // 处理点击空白处关闭
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        // 如果点击的不是模板内容区域和模板按钮，则关闭模板

        if (!target.closest('.semi-popover') && !target.closest('.templateButton')) {
          setShowTemplate(false);
        }
      };

      if (showTemplate) {
        document.addEventListener('click', handleClickOutside);
      }

      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }, [showTemplate]);

    // 处理模板选择
    const handleTemplateSelect = (template: any) => {
      if (onSelectTemplate) {
        onSelectTemplate(template);
      }
    };

    return (
      <div className={styles.templateHeader}>
        <div className={styles.templateHeaderContent}>
          <div className={styles.leftSection}>
            <img src={pen} className={styles.editIcon} />
            <Typography.Text className={styles.dialogueType} strong>
              {title || ''}
            </Typography.Text>
          </div>
          {!NO_TEMPLATE_APPS.includes(appCode) && (
            <div className={styles.rightSection}>
              <Popover
                style={{ position: 'absolute', top: '-538px', right: '-85px' }}
                content={
                  <TemplateContent
                    currNav={currNav}
                    changeNav={changeNav}
                    onSelectTemplate={handleTemplateSelect}
                  />
                }
                trigger="custom"
                position="topLeft"
                showArrow={false}
                className={styles.templatePopover}
                visible={showTemplate}
              >
                <Button
                  theme="light"
                  type="tertiary"
                  className="templateButton"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowTemplate((prev) => !prev);
                  }}
                >
                  <img
                    src={showTemplate ? opentemplate : enlarge}
                    className={styles.templateIcon}
                  />
                  <span style={{ marginLeft: '4px' }}>模板</span>
                </Button>
              </Popover>

              {/* <Button
              theme="borderless"
              type="tertiary"
              className={styles.closeButton}
              onClick={onClose}
            >
              <img src={penclose} />
            </Button> */}
            </div>
          )}
        </div>
      </div>
    );
  },
);

export default TemplateHeader;
