import { getDvaApp } from 'umi';

// 定义 Action 类型（根据实际需求调整）
type DispatchAction = {
  type: string;
  isEdit?: boolean;
  payload?: any;
};

// 缓存 Dva 实例
let cachedDva: any = null;

/**
 * 封装的 dispatch 方法
 * @param action - Dva Action 对象
 */
export const dispatchInUtils = (action: DispatchAction) => {
  if (!cachedDva) {
    cachedDva = getDvaApp();
  }
  cachedDva?._store.dispatch(action);
};

export const getState = () => {
  if (!cachedDva) {
    cachedDva = getDvaApp();
  }
  return cachedDva?._store.getState();
};
