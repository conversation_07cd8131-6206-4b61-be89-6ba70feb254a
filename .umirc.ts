import { defineConfig } from 'umi';

require('dotenv').config({ path: `.env.${process.env.UMI_ENV}` });

export default defineConfig({
  routes: [
    {
      path: '/',
      component: '@/layouts/menu',
      routes: [
        { path: '/', component: '@/pages/index', redirect: '/chat' },
        { path: '/chat', component: '@/pages/Chat' },
        { path: '/chat/knowledge', component: '@/pages/Knowledge' },
        { path: '/chat/office', component: '@/pages/Office' },
        { path: '/chat/smart-engineering', component: '@/pages/SmartManagement' },
        { path: '/chat/smart-management', component: '@/pages/Engineering' },
        { path: '/chat/ai-toolbox', component: '@/pages/Tools' },
        { path: '/chat/:chatId', component: '@/pages/Chat/historyChat' },
        { path: '/chat-read/:chatId', component: '@/pages/Chat/ChatWrite/readChat' },
      ],
    },
    { path: '/ppt-view', component: '@/pages/Chat/pptView' },
    { path: '/chatv2', component: 'Chat', layout: false },
    { path: '/4Alogin', component: 'FourALogin', layout: false },
    { path: '/login', component: 'Login', layout: false },
    { path: '/*', component: '@/pages/404', layout: false },
  ],
  npmClient: 'yarn',
  dva: {},
  cssLoaderModules: {
    // 配置驼峰式使用
    exportLocalsConvention: 'camelCase',
  },
  plugins: [
    '@umijs/plugins/dist/dva',
    '@umijs/plugins/dist/tailwindcss',
    'umi-plugin-keep-alive',
    './plugins/insertAipptScript.ts',
    './plugins/insertLink.ts',
  ],
  define: {
    'process.env.UMI_ENV': process.env.UMI_ENV,
    'process.env.API_URL': process.env.API_URL,
    'process.env.VITE_GLOB_4A_URL': process.env.VITE_GLOB_4A_URL,
    'process.env.AI_PPT_IFRAME_URL': process.env.AI_PPT_IFRAME_URL,
    'process.env.PPT_APP_KEY': process.env.PPT_APP_KEY,
  },
  tailwindcss: {},
  favicons: ['/favicon.ico'],
  title: '瞳界AI应用平台',
  esbuildMinifyIIFE: true,
});
