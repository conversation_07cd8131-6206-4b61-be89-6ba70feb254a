import { saveAs } from 'file-saver';
import JSZip from 'jszip';

export const downloadImagesAsZip = async (imageUrls: string[]) => {
  const zip = new JSZip();
  const promises: Promise<void>[] = [];

  // 逐个下载图片并添加到 ZIP
  imageUrls.forEach((url, index) => {
    const extension = url.split('.').pop();
    const fileName = `image-${index + 1}.${extension || 'jpg'}`; // 自定义文件名
    promises.push(
      fetch(url)
        .then((res) => res.blob())
        .then((blob) => {
          zip.file(fileName, blob);
        }),
    );
  });

  try {
    await Promise.all(promises);
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, 'images.zip'); // 下载 ZIP 文件
  } catch (error) {
    console.error('下载失败:', error);
  }
};
