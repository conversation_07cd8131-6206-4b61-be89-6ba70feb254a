import imgBg from '@/assets/txt2img/img-bg.svg';
import { Image } from '@douyinfe/semi-ui';
import styles from '../index.less';

/**
 * loading 有进度，generating 没有进度，done 完成
 * 注意：和message.status不一样
 * @param status 状态
 * @param imgUrlList 图片列表
 * @param count 图片数量
 * @param imgHeight 图片高度
 * @param messageId 消息ID
 * @param progress 进度
 */
const ImgGeneratingRender: React.FC<{
  status: 'loading' | 'generating' | 'done';
  count?: number;
  height?: number;
  progress?: number;
}> = ({ status, count = 1, progress, height = 261 }) => {
  return (
    <div className={styles.imgGeneratingContainer}>
      <div className={styles.imgGeneratingList}>
        {(status !== 'done' ? Array.from({ length: count }) : []).map((_, idx) => {
          return (
            <div className={styles.imgGeneratingItem} key={idx} style={{ height: height }}>
              {/* 有进度的加载状态 */}
              {status === 'loading' ? (
                <>
                  <div className={styles.imgGeneratingProgressBg}>
                    <div
                      className={styles.imgGeneratingProgressBar}
                      style={{ width: `${progress}%`, position: 'relative' }}
                    >
                      <div className={styles.imgGeneratingProgressBarShine} />
                    </div>
                  </div>
                  <Image
                    className={styles.imgGeneratingImg}
                    style={{ height: height }}
                    width={'174px'}
                    src={imgBg}
                    preview={status !== 'loading'}
                    alt={status === 'loading' ? `loading${idx + 1}` : `result${idx + 1}`}
                  />
                </>
              ) : (
                ''
              )}
              {/* 生成中的加载状态 */}
              {status === 'generating' ? (
                <div className={styles.imgGeneratingBar}>
                  <Image
                    className={styles.imgGeneratingImg}
                    style={{ height: height }}
                    width={'174px'}
                    src={imgBg}
                    preview={false}
                    alt={`result${idx + 1}`}
                  />
                </div>
              ) : (
                ''
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ImgGeneratingRender;
