// src/components/PWAInstallButton/PWAInstallButton.tsx
import WebdownSvg from '@/assets/svg/webdown.svg';
import { Button } from '@douyinfe/semi-ui';
import React from 'react';
import { usePWAInstall } from './hooks/usePWAInstall';
import styles from './index.less';

interface PWAInstallButtonProps {
  className?: string;
  installText?: string;
  installedText?: string;
  errorText?: string;
  onInstalled?: () => void;
  onError?: (error: Error) => void;
}

const PWAInstallButton: React.FC<PWAInstallButtonProps> = ({
  installText = '桌面端',
  onInstalled,
  onError,
}) => {
  const { installApp, error, canInstall, isInstalled } = usePWAInstall();

  const handleInstall = async () => {
    const success = await installApp();
    if (success && onInstalled) {
      onInstalled();
    }
    if (error && onError) {
      onError(error);
    }
  };

  const isDisabled = !canInstall || isInstalled;

  return (
    <>
      {isDisabled ? (
        ''
      ) : (
        <Button
          className={styles.pwaInstallButton}
          theme="borderless"
          type="tertiary"
          icon={<img width={24} height={24} src={WebdownSvg} />}
          onClick={handleInstall}
          disabled={isDisabled}
        >
          <span className={styles.pwaInstallButtonText}>{installText}</span>
        </Button>
      )}
    </>
  );
};

export default PWAInstallButton;
