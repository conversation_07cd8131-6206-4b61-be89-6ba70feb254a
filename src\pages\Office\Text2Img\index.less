.text2Img {
  padding-bottom: 12px;
  max-width: 800px;
  height: 100%;
  margin: 0 auto;
  overflow-y: scroll;

  :global {
    .semi-tabs-content {
      padding: 0 !important;
    }

    .semi-tabs-bar {
      position: sticky;
      top: 0;
      z-index: 999;
      background: #fff;
    }

    .semi-tabs-tab {
      padding: 0 !important;
      height: 35px;
      font-size: 14px !important;
      font-weight: 350 !important;
      color: #000 !important;
    }

    .semi-tabs-tab-single {
      margin-right: 32px !important;
    }

    .semi-tabs-tab:last-child {
      margin-right: 0 !important;
    }

    .semi-tabs-tab-active {
      font-weight: 500 !important;
      border-bottom-color: #000 !important;
    }

    .semi-tabs-bar-arrow {
      display: none !important;
    }
  }
}

.text2Img::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.recentRecordTitle {
  font-size: 12px;
  line-height: 20px;
  color: #3d3d3d;
  margin: 16px 0 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 16px;

  .loadingText {
    font-size: 14px;
    color: #666;
    margin-top: 8px;
  }
}

.emptyMessageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .emptyImage {
    width: 160px;
    height: 160px;
  }

  .emptyMessageText {
    font-size: 14px;
    color: #3d3d3d;
    margin-top: 12px;
  }
}
