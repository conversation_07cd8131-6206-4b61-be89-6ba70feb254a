/**
 * AI工具箱
 */

import amz from '@/assets/svg/AMZ.svg';
import atb from '@/assets/svg/atb.svg';
import bimo from '@/assets/svg/bimo.svg';
import chatexcel from '@/assets/svg/chatexcel.svg';
import deepseek from '@/assets/svg/deepseek.svg';
import doubao from '@/assets/svg/doubao.svg';
import feizhu from '@/assets/svg/feizhu.svg';
import kimi from '@/assets/svg/kimi.svg';
import kozi from '@/assets/svg/kozi.svg';
import mita from '@/assets/svg/mita.svg';
import nami from '@/assets/svg/nami.svg';
import notion from '@/assets/svg/notion.svg';
import pdf from '@/assets/svg/pdf.svg';
import Pixelcut from '@/assets/svg/Pixelcut.svg';
import recompressor from '@/assets/svg/Recompressor.svg';
import treemind from '@/assets/svg/treemind.svg';
import tyqw from '@/assets/svg/tyqw.svg';
import wxyy from '@/assets/svg/wxyy.svg';
import xffy from '@/assets/svg/xffy.svg';
import xfxh from '@/assets/svg/xfxh.svg';
import ydfy from '@/assets/svg/ydfy.svg';
import yuanbao from '@/assets/svg/yuanbao.svg';
import { Card, CardGroup, Typography } from '@douyinfe/semi-ui';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';

const AiToolbox: React.FC = () => {
  const { Text } = Typography;
  const [toolList, setToolList] = useState<any[]>([]);
  const containerRef = useRef(null);

  const onclick = (item: any) => {
    window.open(item.Website, '_blank');
  };
  useEffect(() => {
    setToolList([
      {
        Name: '豆包',
        Function: '抖音旗下 AI 工具，你的智能助手',
        Website: 'https://www.doubao.com/',
        url: doubao,
      },
      {
        Name: '腾讯元宝',
        Function: '腾讯推出的免费 AI 智能助手',
        Website: 'https://yuanbao.tencent.com/',
        url: yuanbao,
      },
      {
        Name: 'deepseek',
        Function: '幻方量化旗下深度求索推出的开源大模型和聊天助手',
        Website: 'https://chat.deepseek.com/',
        url: deepseek,
      },
      {
        Name: '文心一言',
        Function: '百度推出的基于文心大模型的 AI 对话互动工具',
        Website: 'https://yiyan.baidu.com/',
        url: wxyy,
      },
      {
        Name: '通义千问',
        Function: '阿里云推出的一个超大规模的语言模型',
        Website: 'https://tongyi.aliyun.com/qianwen/',
        url: tyqw,
      },
      {
        Name: 'Kimi.ai',
        Function: '会推理解析，能深度思考的 AI 助手',
        Website: 'https://kimi.moonshot.cn/',
        url: kimi,
      },
      {
        Name: '纳米AI搜索',
        Function: '360推出的超级AI搜索引擎',
        Website: 'https://www.n.cn/?ref=feizhuke.com',
        url: nami,
      },
      {
        Name: '智能体开发-扣子',
        Function: '快速搭建一个 AI 智能体',
        Website: 'https://www.coze.cn/',
        url: kozi,
      },
      {
        Name: 'Notion AI',
        Function: 'Notion 旗下的 AI 内容创作助手',
        Website: 'https://www.notion.com/',
        url: notion,
      },
      {
        Name: '秘塔写作猫',
        Function: 'AI 写作，文章自成',
        Website: 'https://xiezuocat.com/',
        url: mita,
      },
      {
        Name: '笔墨公文',
        Function: 'AI 公文写作',
        Website: 'https://www.bimoxiezuo.com/agi?keyfrom=AIdh6_web',
        url: bimo,
      },
      {
        Name: '讯飞星火',
        Function: '免费 AI 会话，写作，搜索，PPT等全能综合智能体',
        Website: 'https://xinghuo.xfyun.cn/desk',
        url: xfxh,
      },
      {
        Name: 'RECOMPRESSOR',
        Function: '免费的在线 AI 图片压缩工具',
        Website: 'https://zh.recompressor.com/?ref=feizhuke.com',
        url: recompressor,
      },
      {
        Name: 'Pixelcut.ai',
        Function: '基于 AI 的图片编辑器',
        Website: 'https://www.pixelcut.ai/?ref=feizhuke.com',
        url: Pixelcut,
      },
      {
        Name: 'TreeMind 树图',
        Function: '文本转为思维导图',
        Website: 'https://shutu.cn/',
        url: treemind,
      },
      {
        Name: 'chatexcel',
        Function: '人工智能的表格处理工具',
        Website: 'https://www.chatexcel.com/#/home?partner_uuid=982ABAC59D5F31868A1C6C74E190CF03',
        url: chatexcel,
      },
      {
        Name: '爱图表',
        Function: 'AI 图表，让每个人都能轻松创建高颜值可视化图表',
        Website: 'https://aitubiao.com/?utm_source=DSF-DHWZ-FZBMY',
        url: atb,
      },
      {
        Name: '讯飞翻译',
        Function: '基于人工智能，支持多种文档格式、文本、语音、图片的互译',
        Website: 'https://fanyi.xfyun.cn/console/trans/doc?ivu=17448931122',
        url: xffy,
      },
      {
        Name: '有道翻译',
        Function: '网易有道翻译，网页版，在线翻译，下载电脑版一款便捷易上手的国产翻译工具',
        Website: 'https://fanyi.youdao.com/index.html#/AITranslate',
        url: ydfy,
      },
      {
        Name: 'PDF数据提取神器',
        Function: '识别 PDF 或图片中的表格、文字内容',
        Website: 'https://webtool.pdflux.com/#/',
        url: pdf,
      },
      {
        Name: '飞猪 ai 导航',
        Function: 'AI 功能模块导航',
        Website: 'https://feizhuke.com/#term-6606',
        url: feizhu,
      },
      {
        Name: 'deepseek提示词',
        Function: '官方提示词最全合集',
        Website: 'https://api-docs.deepseek.com/zh-cn/prompt-library/?ref=feizhuke.com',
        url: deepseek,
      },
      {
        Name: '指令大全',
        Function: '各类提示词',
        Website: 'https://www.amz123.com/tools-prompt',
        url: amz,
      },
    ]);
  }, []);

  useEffect(() => {
    const setContainerHeight = () => {
      if (containerRef.current) {
        // 获取屏幕可视高度
        const windowHeight = window.innerHeight;
        // 计算容器高度（减去顶部 56px）
        const containerHeight = windowHeight - 56;
        // 设置容器高度
        containerRef.current.style.height = `${containerHeight}px`;
      }
    };

    // 初始化时设置高度
    setContainerHeight();

    // 监听窗口大小变化事件，重新设置高度
    window.addEventListener('resize', setContainerHeight);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('resize', setContainerHeight);
    };
  }, []);

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.wrapper}>
        <CardGroup spacing={16}>
          {toolList &&
            toolList.map((item, idx) => (
              <div onClick={() => onclick(item)} key={idx}>
                <Card
                  shadows="hover"
                  title={
                    <div className={styles.cardTitleWrapper}>
                      <img src={item.url} alt="" className={styles.cardTitleIcon} />
                      <div className={styles.title}>{item.Name}</div>
                    </div>
                  }
                  headerLine={false}
                  className={styles.cardContent}
                >
                  <Text
                    ellipsis={{
                      rows: 2,
                      showTooltip: true,
                    }}
                    style={{ width: 162 }}
                    className={styles.word}
                  >
                    {item.Function}
                  </Text>
                </Card>
              </div>
            ))}
        </CardGroup>
      </div>
    </div>
  );
};

export default () => <AiToolbox />;
